from influxdb_client import InfluxDBClient
import pandas as pd
# Connection Data
url = "http://*************:8086"
token = "4SPXQ9_jeeobEkc3hswm2Kdd93C9a3fmchDRIqePg8IzMNem7AQhoo7BCiPFht4cDszS5mPrcjMrxcL5YfPf3g=="
org = "IPQ"
bucket = "IPQ_SensorData"

# Flux Query
query = '''
from(bucket: "IPQ_SensorData")
  |> range(start: -24h)
  |> filter(fn: (r) => r["device_name"] == "IPQ_TH14")
  |> filter(fn: (r) => r["_field"] == "value")
  |> filter(fn: (r) => r["SensorTyp"] == "Temperature Humidity Sensor")
  |> filter(fn: (r) => r["application_name"] == "Klimasensoren")
  |> filter(fn: (r) => r["_measurement"] == "device_frmpayload_data_TempC_SHT")
  |> aggregateWindow(every: 1m, fn: mean, createEmpty: false)
  |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
  |> yield(name: "mean")
'''

# InfluxDB Client
client = InfluxDBClient(url=url, token=token, org=org)
query_api = client.query_api()

# Query 
df = query_api.query_data_frame(query=query, org=org)

# CSV exportieren
df.to_csv("export.csv", index=False)

print(" Export worked! Saved as 'export.csv'")
