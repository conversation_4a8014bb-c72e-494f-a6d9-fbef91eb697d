<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="4fb1c135-49ea-4a13-b79b-3ec883d52ffa" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/dlw/dlw-tools/pwb/optimize_pwb.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F3_doped_26cm_opt_for_1550_SQS/export_EDWA_lens_straight.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DSP_IPQ/ipq_python_lib/Tx_main3.py" beforeDir="false" afterPath="$PROJECT_DIR$/DSP_IPQ/ipq_python_lib/Tx_main3.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PWB_NN/data_preparation/data_generation_pipeline.py" beforeDir="false" afterPath="$PROJECT_DIR$/PWB_NN/data_preparation/data_generation_pipeline.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PWB_NN/paper_plots/PWB_with_evanescent_tapers.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PWB_NN/paper_plots/plot_trajectory.py" beforeDir="false" afterPath="$PROJECT_DIR$/PWB_NN/paper_plots/plot_trajectory.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PWB_NN/training/build_model.py" beforeDir="false" afterPath="$PROJECT_DIR$/PWB_NN/training/build_model.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PWB_NN/trajectory_optimization/analysis/NN_vs_conventional_design.py" beforeDir="false" afterPath="$PROJECT_DIR$/PWB_NN/trajectory_optimization/analysis/NN_vs_conventional_design.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PWB_NN/visualization/visualize_mesh.py" beforeDir="false" afterPath="$PROJECT_DIR$/PWB_NN/visualization/visualize_mesh.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PyFocus/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/amf_pdk_32/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/device_wrappers/Ando_AQ6317B_OSA/AQ6317B_wrapper.py" beforeDir="false" afterPath="$PROJECT_DIR$/device_wrappers/Ando_AQ6317B_OSA/AQ6317B_wrapper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/device_wrappers/Goldeye/goldeye_G034_TEC1.py" beforeDir="false" afterPath="$PROJECT_DIR$/device_wrappers/Goldeye/goldeye_G034_TEC1.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/device_wrappers/II_VI_waveshaper/wsp_generator.py" beforeDir="false" afterPath="$PROJECT_DIR$/device_wrappers/II_VI_waveshaper/wsp_generator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/device_wrappers/Keysight_B2902A_driver/B2902A_wrapper.py" beforeDir="false" afterPath="$PROJECT_DIR$/device_wrappers/Keysight_B2902A_driver/B2902A_wrapper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/device_wrappers/NIDAQmx_driver/NIDAQmx_wrapper.py" beforeDir="false" afterPath="$PROJECT_DIR$/device_wrappers/NIDAQmx_driver/NIDAQmx_wrapper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/device_wrappers/NIDAQmx_driver/NIDAQmx_wrapper_clean.py" beforeDir="false" afterPath="$PROJECT_DIR$/device_wrappers/NIDAQmx_driver/NIDAQmx_wrapper_clean.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/diffractio/diffractio/scalar_fields_XYZ.py" beforeDir="false" afterPath="$PROJECT_DIR$/diffractio/diffractio/scalar_fields_XYZ.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/diffractio/diffractio/vector_fields_XZ.py" beforeDir="false" afterPath="$PROJECT_DIR$/diffractio/diffractio/vector_fields_XZ.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/diffractio/docs/source/functioning/artifacts_bpm.ipynb" beforeDir="false" afterPath="$PROJECT_DIR$/diffractio/docs/source/functioning/artifacts_bpm.ipynb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/diffractio_old/diffractio/scalar_fields_XYZ.py" beforeDir="false" afterPath="$PROJECT_DIR$/diffractio_old/diffractio/scalar_fields_XYZ.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/diffractio_old/diffractio/vector_fields_XZ.py" beforeDir="false" afterPath="$PROJECT_DIR$/diffractio_old/diffractio/vector_fields_XZ.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/dlw/dlw-control/dlwControl2/geometry/mesh.py" beforeDir="false" afterPath="$PROJECT_DIR$/dlw/dlw-control/dlwControl2/geometry/mesh.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/dlw/dlw-control/dlwControl2/photonic_bond/pwb.py" beforeDir="false" afterPath="$PROJECT_DIR$/dlw/dlw-control/dlwControl2/photonic_bond/pwb.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/dlw/dlw-control/dlwControl2/tools/nanoscribe/auto_bonding/chip_collection/hhi_edge_emitter.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/dlw/dlw-control/dlwControl2/tools/nanoscribe/auto_bonding/chip_collection/hhi_edge_emitter.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/dlw/dlw-tools/InP_tapers/EdgeDetector.py" beforeDir="false" afterPath="$PROJECT_DIR$/dlw/dlw-tools/InP_tapers/EdgeDetector.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/dlw/dlw-tools/InP_tapers/EdgeDetector2.py" beforeDir="false" afterPath="$PROJECT_DIR$/dlw/dlw-tools/InP_tapers/EdgeDetector2.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/dlw/dlw-tools/pwb/export_pwb.py" beforeDir="false" afterPath="$PROJECT_DIR$/dlw/dlw-tools/pwb/export_pwb.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/dlw/nanoscribe-dlw/nanoscribe_dlw/bin/worker_config.py" beforeDir="false" afterPath="$PROJECT_DIR$/dlw/nanoscribe-dlw/nanoscribe_dlw/bin/worker_config.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/dlw/pypylon/cython/factory.pyx" beforeDir="false" afterPath="$PROJECT_DIR$/dlw/pypylon/cython/factory.pyx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ecl_controller/Keysight_B2902A_driver/B2902A_wrapper.py" beforeDir="false" afterPath="$PROJECT_DIR$/ecl_controller/Keysight_B2902A_driver/B2902A_wrapper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ecl_controller/NIDAQmx_driver/NIDAQmx_wrapper.py" beforeDir="false" afterPath="$PROJECT_DIR$/ecl_controller/NIDAQmx_driver/NIDAQmx_wrapper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ecl_controller/resonance_tuning/check_operation_points.py" beforeDir="false" afterPath="$PROJECT_DIR$/ecl_controller/resonance_tuning/check_operation_points.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ecl_controller/resonance_tuning/config.py" beforeDir="false" afterPath="$PROJECT_DIR$/ecl_controller/resonance_tuning/config.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ecl_controller/resonance_tuning/tuning_with_CT400.py" beforeDir="false" afterPath="$PROJECT_DIR$/ecl_controller/resonance_tuning/tuning_with_CT400.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ecl_controller/test_PSU.py" beforeDir="false" afterPath="$PROJECT_DIR$/ecl_controller/test_PSU.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ecl_controller/utilities/curve_fitting.py" beforeDir="false" afterPath="$PROJECT_DIR$/ecl_controller/utilities/curve_fitting.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ecl_controller/utilities/file_handling.py" beforeDir="false" afterPath="$PROJECT_DIR$/ecl_controller/utilities/file_handling.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ecl_controller/utilities/interpolate_methods.py" beforeDir="false" afterPath="$PROJECT_DIR$/ecl_controller/utilities/interpolate_methods.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ecl_controller/utilities/plot_measurement.py" beforeDir="false" afterPath="$PROJECT_DIR$/ecl_controller/utilities/plot_measurement.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ecl_controller/utilities/utils.py" beforeDir="false" afterPath="$PROJECT_DIR$/ecl_controller/utilities/utils.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lens_design/SNSPD/lens_optimizer_multilens3_plane_wave_paper_figure.py" beforeDir="false" afterPath="$PROJECT_DIR$/lens_design/SNSPD/lens_optimizer_multilens3_plane_wave_paper_figure.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lens_design/ellipsoidal_lens/generate_ellipsoid_cad.py" beforeDir="false" afterPath="$PROJECT_DIR$/lens_design/ellipsoidal_lens/generate_ellipsoid_cad.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lens_design/prism_design.py" beforeDir="false" afterPath="$PROJECT_DIR$/lens_design/prism_design.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lens_design/ray_optics/utils.py" beforeDir="false" afterPath="$PROJECT_DIR$/lens_design/ray_optics/utils.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lightpipes/Examples/Commands/Begin.py" beforeDir="false" afterPath="$PROJECT_DIR$/lightpipes/Examples/Commands/Begin.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lightpipes/LightPipes/__init__.py" beforeDir="false" afterPath="$PROJECT_DIR$/lightpipes/LightPipes/__init__.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lightpipes/LightPipes/core.py" beforeDir="false" afterPath="$PROJECT_DIR$/lightpipes/LightPipes/core.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lightpipes/LightPipes/lenses.py" beforeDir="false" afterPath="$PROJECT_DIR$/lightpipes/LightPipes/lenses.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lightpipes/LightPipes/misc.py" beforeDir="false" afterPath="$PROJECT_DIR$/lightpipes/LightPipes/misc.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lightpipes/LightPipes/propagators.py" beforeDir="false" afterPath="$PROJECT_DIR$/lightpipes/LightPipes/propagators.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lightpipes/LightPipes/sources.py" beforeDir="false" afterPath="$PROJECT_DIR$/lightpipes/LightPipes/sources.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lightpipes/LightPipes/userfunc.py" beforeDir="false" afterPath="$PROJECT_DIR$/lightpipes/LightPipes/userfunc.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lightpipes/LightPipes/zernike.py" beforeDir="false" afterPath="$PROJECT_DIR$/lightpipes/LightPipes/zernike.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lightpipes/docs/_downloads/54dbcd11ac000826024941a4b489ac78/Begin.py" beforeDir="false" afterPath="$PROJECT_DIR$/lightpipes/docs/_downloads/54dbcd11ac000826024941a4b489ac78/Begin.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lightpipes/docs/plot_directive/Examples/Commands/Begin.py" beforeDir="false" afterPath="$PROJECT_DIR$/lightpipes/docs/plot_directive/Examples/Commands/Begin.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lightpipes/sphinx-sources/Examples/Commands/Begin.py" beforeDir="false" afterPath="$PROJECT_DIR$/lightpipes/sphinx-sources/Examples/Commands/Begin.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lightpipes/sphinx-sources/MyFunctions/MyLightPipesFunctions.py" beforeDir="false" afterPath="$PROJECT_DIR$/lightpipes/sphinx-sources/MyFunctions/MyLightPipesFunctions.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lightpipes/sphinx-sources/manual.rst" beforeDir="false" afterPath="$PROJECT_DIR$/lightpipes/sphinx-sources/manual.rst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lightpipes/test_Begin_dtype.py" beforeDir="false" afterPath="$PROJECT_DIR$/lightpipes/test_Begin_dtype.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lightpipes/test_UsePyFFTW.py" beforeDir="false" afterPath="$PROJECT_DIR$/lightpipes/test_UsePyFFTW.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lumpy/PWB_generation/generate_pwb_parameter_space.py" beforeDir="false" afterPath="$PROJECT_DIR$/lumpy/PWB_generation/generate_pwb_parameter_space.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lumpy/analyzation/result_analyser.py" beforeDir="false" afterPath="$PROJECT_DIR$/lumpy/analyzation/result_analyser.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lumpy/scripts/hybrid_comb_lens.py" beforeDir="false" afterPath="$PROJECT_DIR$/lumpy/scripts/hybrid_comb_lens.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lumpy/utils/load_modules.py" beforeDir="false" afterPath="$PROJECT_DIR$/lumpy/utils/load_modules.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lumpy/utils/run_simulation.py" beforeDir="false" afterPath="$PROJECT_DIR$/lumpy/utils/run_simulation.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/measurement/MFD_measurement/mfd_measurer.py" beforeDir="false" afterPath="$PROJECT_DIR$/measurement/MFD_measurement/mfd_measurer.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/measurement/MFD_measurement/util/move_cam.py" beforeDir="false" afterPath="$PROJECT_DIR$/measurement/MFD_measurement/util/move_cam.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/my-ceviche/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/my-ceviche/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/myutils/IPQ-THSensors.py" beforeDir="false" afterPath="$PROJECT_DIR$/myutils/IPQ-THSensors.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/myutils/Sonata_helper.py" beforeDir="false" afterPath="$PROJECT_DIR$/myutils/Sonata_helper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/phase_recovery/gerchberg_saxton.py" beforeDir="false" afterPath="$PROJECT_DIR$/phase_recovery/gerchberg_saxton.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/phase_recovery/old/gerchberg_saxton_backup.py" beforeDir="false" afterPath="$PROJECT_DIR$/phase_recovery/old/gerchberg_saxton_backup.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/build/csharp_vsproj_files/mmapi_cpp_dll.vcxproj" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/build/csharp_vsproj_files/mmapi_cpp_dll.vcxproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/build/csharp_vsproj_files/mmapi_csharp.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/build/csharp_vsproj_files/mmapi_csharp.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/build/csharp_vsproj_files/mmapi_planecut_test.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/build/csharp_vsproj_files/mmapi_planecut_test.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/build/csharp_vsproj_files_unity/mmapi_cpp_dll.vcxproj" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/build/csharp_vsproj_files_unity/mmapi_cpp_dll.vcxproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/build/csharp_vsproj_files_unity/mmapi_csharp.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/build/csharp_vsproj_files_unity/mmapi_csharp.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/build/csharp_vsproj_files_unity/mmapi_planecut_test.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/build/csharp_vsproj_files_unity/mmapi_planecut_test.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/build/swigwin/autogen.sh" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/build/swigwin/autogen.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/build/test.py" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/build/test.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/cpp/PackedMeshDemo/PackedMeshDemo.vcxproj" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/cpp/PackedMeshDemo/PackedMeshDemo.vcxproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/csharp/CommandDemos/CommandDemos.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/csharp/CommandDemos/CommandDemos.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/csharp/LinkedMeshDemo/LinkedMeshDemo.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/csharp/LinkedMeshDemo/LinkedMeshDemo.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/csharp/mm/mm.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/csharp/mm/mm.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/distrib/documentation/python_html/_static/underscore-1.3.1.js" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/distrib/documentation/python_html/_static/underscore-1.3.1.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/distrib/python/test.py" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/distrib/python/test.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/distrib/python_osx/test.py" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/distrib/python_osx/test.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/python/doc/conf.py" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/python/doc/conf.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/python/examples/demo_02_spatialtests.py" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/python/examples/demo_02_spatialtests.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/python/mm/mm_math.py" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/python/mm/mm_math.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/python/mm/packedMesh.py" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/python/mm/packedMesh.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/mmapi/python/mm/tool.py" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/mmapi/python/mm/tool.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/precompensation/mm/mm_math.py" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/precompensation/mm/mm_math.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/precompensation/mm/packedMesh.py" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/precompensation/mm/packedMesh.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/precompensation/precompensation/mm/tool.py" beforeDir="false" afterPath="$PROJECT_DIR$/precompensation/precompensation/mm/tool.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/process_measurement/OBR_processing/obr_data_processing_CLEO_plot.py" beforeDir="false" afterPath="$PROJECT_DIR$/process_measurement/OBR_processing/obr_data_processing_CLEO_plot.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/process_measurement/OSA/plot_EDWA_spectrum.py" beforeDir="false" afterPath="$PROJECT_DIR$/process_measurement/OSA/plot_EDWA_spectrum.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/process_measurement/OSA/plot_QDMLLD_spectrum.py" beforeDir="false" afterPath="$PROJECT_DIR$/process_measurement/OSA/plot_QDMLLD_spectrum.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/process_measurement/WDM/plot_BER_over_channel.py" beforeDir="false" afterPath="$PROJECT_DIR$/process_measurement/WDM/plot_BER_over_channel.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pwbpbs/processes_old/hhi_hcsel.py" beforeDir="false" afterPath="$PROJECT_DIR$/pwbpbs/processes_old/hhi_hcsel.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pyemmodes/pyEMModes/modesolvers/mode.py" beforeDir="false" afterPath="$PROJECT_DIR$/pyemmodes/pyEMModes/modesolvers/mode.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ripple/Inventor/build_straight_lens_side_print.py" beforeDir="false" afterPath="$PROJECT_DIR$/ripple/Inventor/build_straight_lens_side_print.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F3_doped_26cm_opt_for_1550_SQS/01_straight_EDWA_lens_single_fit.py" beforeDir="false" afterPath="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F3_doped_26cm_opt_for_1550_SQS/01_straight_EDWA_lens_single_fit.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ripple/scripts/Rotating_lidar_David/mfd40_single_surf_8deg.py" beforeDir="false" afterPath="$PROJECT_DIR$/ripple/scripts/Rotating_lidar_David/mfd40_single_surf_8deg.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ripple/structures.py" beforeDir="false" afterPath="$PROJECT_DIR$/ripple/structures.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ripple/surfaces.py" beforeDir="false" afterPath="$PROJECT_DIR$/ripple/surfaces.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ripple/wpms.py" beforeDir="false" afterPath="$PROJECT_DIR$/ripple/wpms.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wave_propagation_method/designs/Hybrid_combs_packaging/low_backreflection/reversed_path_optimized.py" beforeDir="false" afterPath="$PROJECT_DIR$/wave_propagation_method/designs/Hybrid_combs_packaging/low_backreflection/reversed_path_optimized.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wave_propagation_method/wpm.py" beforeDir="false" afterPath="$PROJECT_DIR$/wave_propagation_method/wpm.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/waveprop/waveprop/slm.py" beforeDir="false" afterPath="$PROJECT_DIR$/waveprop/waveprop/slm.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wavetorch/study/vanilla/script_all_params.py" beforeDir="false" afterPath="$PROJECT_DIR$/wavetorch/study/vanilla/script_all_params.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wavetorch/study/vanilla/script_params.py" beforeDir="false" afterPath="$PROJECT_DIR$/wavetorch/study/vanilla/script_params.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wavetorch/study/vanilla/vanilla_rnn.py" beforeDir="false" afterPath="$PROJECT_DIR$/wavetorch/study/vanilla/vanilla_rnn.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wavetorch/study/vowel_train.py" beforeDir="false" afterPath="$PROJECT_DIR$/wavetorch/study/vowel_train.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wavetorch/study/vowel_train_sklearn.py" beforeDir="false" afterPath="$PROJECT_DIR$/wavetorch/study/vowel_train_sklearn.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/myutils" />
    <option name="ROOT_SYNC" value="DONT_SYNC" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/pump_to_EDWA/straight/straight_pump_to_EDWA.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/bvwpm/scripts/OPALID_Cyl/Ellipsoid_lens/sweep_b_d.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/bvwpm/scripts/OPALID_Cyl/cylinder_lens_cut.py" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2jKTwpPuvUyP1q6OMT1JfE4yoEw" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "Python tests.Doctest via pytest for B2902A_wrapper_ai.KeysightB2902A.executor": "Run",
    "Python tests.Doctest via pytest in B2902A_wrapper_ai.py.executor": "Run",
    "Python tests.pytest in test_TLB6700.py.executor": "Run",
    "Python tests.pytest in test_pml.py.executor": "Run",
    "Python tests.pytest in test_pyfocus.py.executor": "Debug",
    "Python.01_QD_MLLD_to_SiN (1).executor": "Run",
    "Python.01_QD_MLLD_to_SiN.executor": "Debug",
    "Python.01_QD_MLLD_to_SiN_prism_v2 (1).executor": "Run",
    "Python.01_QD_MLLD_to_SiN_prism_v2.executor": "Run",
    "Python.01_QD_MLLD_to_SiN_prism_v2_CLEO_plot.executor": "Run",
    "Python.01_QD_MLLD_to_SiN_prism_v2_offset_tolerance.executor": "Run",
    "Python.01_straight_EDWA_lens_d4sigma_fit (1).executor": "Run",
    "Python.01_straight_EDWA_lens_d4sigma_fit.executor": "Run",
    "Python.01_straight_EDWA_lens_single_fit (1).executor": "Run",
    "Python.01_straight_EDWA_lens_single_fit.executor": "Run",
    "Python.01_straight_lens_d4sigma_fit.executor": "Run",
    "Python.02_straight_fiber_lens (1).executor": "Run",
    "Python.02_straight_fiber_lens.executor": "Run",
    "Python.03_7deg_EDWA_lens_d4sigma_fit (1).executor": "Debug",
    "Python.03_7deg_EDWA_lens_d4sigma_fit (2).executor": "Run",
    "Python.03_7deg_EDWA_lens_d4sigma_fit.executor": "Run",
    "Python.03_7deg_EDWA_lens_single_fit (1).executor": "Run",
    "Python.03_7deg_EDWA_lens_single_fit.executor": "Run",
    "Python.03_8deg_EDWA_lens_d4sigma_fit.executor": "Run",
    "Python.03_8deg_EDWA_lens_single_fit (1).executor": "Run",
    "Python.03_8deg_EDWA_lens_single_fit.executor": "Run",
    "Python.04_7deg_fiber_lens.executor": "Debug",
    "Python.04_8deg_fiber_lens (1).executor": "Run",
    "Python.04_8deg_fiber_lens.executor": "Run",
    "Python.04_angled_7deg_EDWA_to_fiber (1).executor": "Run",
    "Python.04_angled_7deg_EDWA_to_fiber.executor": "Run",
    "Python.05_7deg_1480.executor": "Run",
    "Python.05_7deg_EDWA_to_fiber (1).executor": "Run",
    "Python.05_7deg_EDWA_to_fiber (2).executor": "Run",
    "Python.05_7deg_EDWA_to_fiber.executor": "Run",
    "Python.05_7deg_EDWA_to_fiber_not_opt.executor": "Run",
    "Python.05_7deg_EDWA_to_fiber_used (1).executor": "Run",
    "Python.05_7deg_EDWA_to_fiber_used.executor": "Run",
    "Python.05_8deg_EDWA_to_fiber (1).executor": "Run",
    "Python.05_8deg_EDWA_to_fiber.executor": "Run",
    "Python.05_8deg_EDWA_to_fiber_used (1).executor": "Run",
    "Python.05_8deg_EDWA_to_fiber_used (2).executor": "Run",
    "Python.05_8deg_EDWA_to_fiber_used.executor": "Run",
    "Python.10deg_EDWA_lens.executor": "Run",
    "Python.10deg_pump_lens.executor": "Run",
    "Python.11_straight_EDWA_lens_gaussian_fit_30MFD.executor": "Run",
    "Python.12_straight_EDWA_lens_gaussian_fit_25MFD.executor": "Run",
    "Python.21_straight_fiber_lens_SQS_30MFD.executor": "Run",
    "Python.22_straight_fiber_lens_SQS_25MFD.executor": "Run",
    "Python.22_straight_fiber_lens_vlink_30MFD.executor": "Run",
    "Python.31_straight_fiber_lens_vlink_30MFD.executor": "Run",
    "Python.32_straight_fiber_lens_vlink_25MFD.executor": "Run",
    "Python.5deg_pump_to_EDWA (1).executor": "Run",
    "Python.5deg_pump_to_EDWA.executor": "Run",
    "Python.810nm_LF.executor": "Run",
    "Python.8deg_EDWA_lens.executor": "Run",
    "Python.8deg_pump_lens.executor": "Run",
    "Python.8deg_pump_to_EDWA.executor": "Run",
    "Python.AMF22_EC (1).executor": "Run",
    "Python.AMF22_EC.executor": "Run",
    "Python.AMF_MFD40.executor": "Run",
    "Python.AMF_MFD50.executor": "Run",
    "Python.AMF_MFD50_2surf.executor": "Run",
    "Python.AMF_lens_50_air_gap.executor": "Run",
    "Python.B2902A_ai.executor": "Run",
    "Python.B2902A_wrapper.executor": "Run",
    "Python.B2902A_wrapper_temp.executor": "Run",
    "Python.DTU_.executor": "Run",
    "Python.DTU_DFB.executor": "Run",
    "Python.DTU_SiC (1).executor": "Run",
    "Python.DV1550AA_wrapper.executor": "Run",
    "Python.EDWA_lens.executor": "Run",
    "Python.EPFL_350nm_test_wg.executor": "Run",
    "Python.EPFL_500nm_test_wg.executor": "Run",
    "Python.EPFL_D143_LGT.executor": "Run",
    "Python.EPFL_D143_LGT_F2.executor": "Debug",
    "Python.EPFL_D143_LGT_angled.executor": "Run",
    "Python.EPFL_D174_01.executor": "Run",
    "Python.EPFL_D211.executor": "Run",
    "Python.EPFL_D211_F1_F3.executor": "Run",
    "Python.EPFL_D211_doped_26cm.executor": "Run",
    "Python.EPFL_D211_doped_26cm_with_lens.executor": "Run",
    "Python.EPFL_D213.executor": "Run",
    "Python.EPFL_D213_doped.executor": "Run",
    "Python.EPFL_D213_doped_17-22-27cm.executor": "Run",
    "Python.EPFL_D213_doped_27cm.executor": "Run",
    "Python.EPFL_SemiNex_pump.executor": "Run",
    "Python.EPFL_SiN_550nm_taper.executor": "Run",
    "Python.ERC (1).executor": "Run",
    "Python.ERC.executor": "Run",
    "Python.Extract_facet_reflection (1).executor": "Run",
    "Python.FP_resonance (1).executor": "Run",
    "Python.FP_resonance.executor": "Run",
    "Python.Figure_S13_chip_facet_reflectivity.executor": "Run",
    "Python.FindDevices.executor": "Run",
    "Python.GUI_10kHz.executor": "Run",
    "Python.GUI_2ch.executor": "Run",
    "Python.GUI_2ch_plot.executor": "Run",
    "Python.GUI_multi_ch_plot.executor": "Run",
    "Python.HHI_QWMLLD_49p7_A2.executor": "Run",
    "Python.IPQ-THSensors.executor": "Run",
    "Python.InP_refr_tilted_pretest.executor": "Run",
    "Python.LIV_PI_curve.executor": "Run",
    "Python.LIV_measurement.executor": "Debug",
    "Python.LTB1_VOA_wrapper.executor": "Run",
    "Python.Ligentec_SiN_Ring_angled_facet (1).executor": "Run",
    "Python.Ligentec_SiN_Ring_angled_facet.executor": "Run",
    "Python.Ligentec_SiN_Sagnac_mirror.executor": "Run",
    "Python.Ligentec_refractive_index.executor": "Run",
    "Python.Lorentzian_linewidth.executor": "Run",
    "Python.MFD40_1surf (1).executor": "Run",
    "Python.MFD40_2surf.executor": "Run",
    "Python.MFD_100.executor": "Run",
    "Python.MFD_30.executor": "Run",
    "Python.Multilane_C5_straight_tapers.executor": "Run",
    "Python.Multilane_C5_taper1p05_0p55um.executor": "Run",
    "Python.NewFocus_TLB6700_wrapper.executor": "Run",
    "Python.OBR_rename.executor": "Debug",
    "Python.OPALID_HHI_2021_updated.executor": "Run",
    "Python.OSA_save_spectrum.executor": "Run",
    "Python.OWF_PS04.executor": "Run",
    "Python.OrionEDFA_wrapper.executor": "Run",
    "Python.PML_reflection_investigate.executor": "Run",
    "Python.PMxxx using ctypes - Python 3.executor": "Debug",
    "Python.PMxxx_SCPI_pyvisa (1).executor": "Run",
    "Python.PMxxx_SCPI_pyvisa.executor": "Run",
    "Python.PWB_test_D141A_F5_C3.executor": "Run",
    "Python.PWB_test_D166.executor": "Run",
    "Python.Part c.executor": "Run",
    "Python.Part d.executor": "Run",
    "Python.PhotonX_DFB_1560_30mfd.executor": "Run",
    "Python.QAM.executor": "Debug",
    "Python.QDMLLD_49p7_J5 (1).executor": "Run",
    "Python.QDMLLD_49p7_J5.executor": "Run",
    "Python.QDMLLD_50GHz.executor": "Run",
    "Python.QDMLLD_to_SiN.executor": "Run",
    "Python.QDMLLD_to_SiN_d1.executor": "Run",
    "Python.QDMLLD_to_SiN_d2.executor": "Run",
    "Python.QDMLLD_to_SiN_d3.executor": "Run",
    "Python.QDMLLD_to_SiN_d4 (1).executor": "Debug",
    "Python.QD_MLLD_lens (1).executor": "Run",
    "Python.QD_MLLD_lens.executor": "Run",
    "Python.QD_MLLD_lens_F1.executor": "Debug",
    "Python.QD_MLLD_lens_F2.executor": "Run",
    "Python.QD_MLLD_lens_debug.executor": "Run",
    "Python.QD_MLLD_lens_dummy.executor": "Run",
    "Python.QD_MLLD_only_tilt.executor": "Run",
    "Python.QD_MLLD_to_SiN.executor": "Debug",
    "Python.QD_MLLD_to_SiN_x_offset.executor": "Debug",
    "Python.QPSK.executor": "Run",
    "Python.SMF_expander_50.executor": "Run",
    "Python.SMF_lens.executor": "Run",
    "Python.SMF_to_AMF_EC (1).executor": "Run",
    "Python.SMF_to_AMF_EC.executor": "Run",
    "Python.SMF_to_AMF_EC_mohamed.executor": "Run",
    "Python.SemiNex_pump_1.executor": "Run",
    "Python.SemiNex_pump_186-2.executor": "Run",
    "Python.SemiNex_pump_186-2_with_lens.executor": "Run",
    "Python.SemiNex_pump_186Test-2_with_lens.executor": "Run",
    "Python.SiC_1560_30mfd_input.executor": "Run",
    "Python.SiC_1560_30mfd_output.executor": "Run",
    "Python.SiN_lens (1).executor": "Run",
    "Python.SiN_lens.executor": "Run",
    "Python.Simple_lens.executor": "Run",
    "Python.Sonata_helper.executor": "Run",
    "Python.THz_independent.executor": "Run",
    "Python.THz_utils.executor": "Run",
    "Python.TIR.executor": "Run",
    "Python.Test.executor": "Run",
    "Python.Th14.executor": "Run",
    "Python.VLINK_16ch_pitch127um_lidless_fiberarray.executor": "Run",
    "Python.VLINK_16ch_pitch127um_lidless_fiberarray_with_lens.executor": "Run",
    "Python.ai_test.executor": "Run",
    "Python.angled_7deg_EDWA_to_fiber.executor": "Run",
    "Python.angled_7deg_lens_fiber.executor": "Run",
    "Python.angled_7deg_lens_gaussian_mfd_fit.executor": "Run",
    "Python.anti-backreflection_surface.executor": "Run",
    "Python.array_io.executor": "Run",
    "Python.beam_angle_debug.executor": "Run",
    "Python.beam_direction_debug.executor": "Run",
    "Python.boundary_conditions.executor": "Run",
    "Python.brewster_angle.executor": "Run",
    "Python.build_SiN_lens.executor": "Run",
    "Python.build_fiber_lens_side_print.executor": "Run",
    "Python.build_laser_lens.executor": "Run",
    "Python.build_lens_with_angled_gap.executor": "Run",
    "Python.build_straight_lens.executor": "Run",
    "Python.build_straight_lens_side_print.executor": "Run",
    "Python.build_straight_lens_top_print_Sonata.executor": "Run",
    "Python.bwpms (1).executor": "Run",
    "Python.bwpms.executor": "Run",
    "Python.cache.executor": "Run",
    "Python.cal_delayline_mismatch.executor": "Run",
    "Python.calibrate_lens.executor": "Run",
    "Python.camera.executor": "Run",
    "Python.check_field.executor": "Run",
    "Python.chip_rotation_along_y.executor": "Run",
    "Python.compress_pdf.executor": "Run",
    "Python.concave_imported_source.executor": "Run",
    "Python.constellation_diagram.executor": "Run",
    "Python.control_TLB6700 (1).executor": "Run",
    "Python.control_TLB6700.executor": "Run",
    "Python.coupling_axial_offset.executor": "Run",
    "Python.coupling_different_mfd.executor": "Run",
    "Python.coupling_optimizer.executor": "Run",
    "Python.coupling_translation_offset.executor": "Run",
    "Python.crop_img.executor": "Run",
    "Python.cut_lens (1).executor": "Run",
    "Python.cut_lens.executor": "Run",
    "Python.cylinder_lens.executor": "Run",
    "Python.cylinder_lens2.executor": "Debug",
    "Python.cylinder_lens_angled.executor": "Run",
    "Python.cylinder_lens_cut (1).executor": "Run",
    "Python.cylinder_lens_cut.executor": "Run",
    "Python.cylinder_lens_cut_left_ext.executor": "Run",
    "Python.d-BF.executor": "Run",
    "Python.debug.executor": "Run",
    "Python.dev.executor": "Run",
    "Python.different_solvers.executor": "Run",
    "Python.diss_figure.executor": "Run",
    "Python.double_CCS_low_noise.executor": "Run",
    "Python.dummy.executor": "Run",
    "Python.evan.executor": "Run",
    "Python.evenescent_coupling_blender.executor": "Run",
    "Python.example.executor": "Run",
    "Python.export_DFB_lens.executor": "Run",
    "Python.export_EDWA_lens (1).executor": "Run",
    "Python.export_EDWA_lens.executor": "Run",
    "Python.export_EDWA_lens_straight.executor": "Run",
    "Python.export_SiC_lens.executor": "Run",
    "Python.export_fiber_lens (1).executor": "Run",
    "Python.export_fiber_lens.executor": "Run",
    "Python.export_mesh.executor": "Run",
    "Python.export_mesh_EDWA.executor": "Run",
    "Python.export_model_EDWA.executor": "Run",
    "Python.export_model_EDWA_11.executor": "Run",
    "Python.export_model_QDMLLD_lens.executor": "Run",
    "Python.export_model_QDMLLD_lens_d2.executor": "Run",
    "Python.export_model_QDMLLD_lens_d3.executor": "Run",
    "Python.export_model_QDMLLD_lens_d4.executor": "Run",
    "Python.export_model_QDMLLD_lens_v2.executor": "Run",
    "Python.export_model_QD_lens (1).executor": "Run",
    "Python.export_model_QD_lens.executor": "Debug",
    "Python.export_model_SiN_lens.executor": "Debug",
    "Python.export_model_SiN_lens_d3.executor": "Run",
    "Python.export_model_SiN_lens_d4.executor": "Run",
    "Python.export_model_default_fit.executor": "Run",
    "Python.export_model_fiber.executor": "Run",
    "Python.export_model_gaussian_fit.executor": "Run",
    "Python.export_pump_lens.executor": "Run",
    "Python.export_pump_lens2.executor": "Run",
    "Python.facet_reflection_characterization (1).executor": "Run",
    "Python.far_field.executor": "Run",
    "Python.fastMode.executor": "Run",
    "Python.field.executor": "Run",
    "Python.field_distribution.executor": "Run",
    "Python.front_end_ui.executor": "Run",
    "Python.gaussian_beam.executor": "Run",
    "Python.goldeye_G034_TEC1 (1).executor": "Run",
    "Python.goldeye_G034_TEC1.executor": "Run",
    "Python.gpt_r_extraction.executor": "Run",
    "Python.hybrid_comb_lens.executor": "Run",
    "Python.image_histogram.executor": "Run",
    "Python.image_into_gif.executor": "Run",
    "Python.image_to_video.executor": "Run",
    "Python.images_to_video.executor": "Run",
    "Python.import_data.executor": "Run",
    "Python.imported_cst_field_test.executor": "Run",
    "Python.imported_field_and_whole_lens (1).executor": "Run",
    "Python.imported_field_and_whole_lens.executor": "Run",
    "Python.imported_lens.executor": "Run",
    "Python.imported_lens_single_excitation.executor": "Run",
    "Python.imported_structures.executor": "Run",
    "Python.io.executor": "Run",
    "Python.jakovljevic_2594161_PS04_TM_code.executor": "Debug",
    "Python.learn_multiprocessing.executor": "Run",
    "Python.learn_signal.executor": "Run",
    "Python.lena_has_returned.executor": "Run",
    "Python.lens_optimizer_multilens2_plane_wave.executor": "Run",
    "Python.lens_optimizer_multilens3.executor": "Debug",
    "Python.lens_optimizer_multilens3_plane_wave_paper_figure.executor": "Run",
    "Python.load_modules.executor": "Run",
    "Python.loadfilter (1).executor": "Debug",
    "Python.loadfilter.executor": "Debug",
    "Python.m2_display.executor": "Run",
    "Python.main.executor": "Run",
    "Python.main_v2.executor": "Run",
    "Python.masks.executor": "Run",
    "Python.meas.executor": "Run",
    "Python.merge_pdf.executor": "Run",
    "Python.mesh.executor": "Run",
    "Python.mfd40_single_surf.executor": "Run",
    "Python.mfd40_single_surf_8deg.executor": "Run",
    "Python.mfd50.executor": "Run",
    "Python.mfd50_8_deg.executor": "Run",
    "Python.mfd_calculation_debug.executor": "Run",
    "Python.mfd_fit.executor": "Debug",
    "Python.mfd_meas.executor": "Run",
    "Python.mfd_measurer (1).executor": "Run",
    "Python.model_importer.executor": "Debug",
    "Python.monitors.executor": "Run",
    "Python.move_cam.executor": "Run",
    "Python.noise_figure.executor": "Debug",
    "Python.non-symmetrical_QAM.executor": "Run",
    "Python.obj.executor": "Debug",
    "Python.obj2ipt.executor": "Run",
    "Python.oblique_interface (1).executor": "Debug",
    "Python.oblique_interface.executor": "Run",
    "Python.obr_data_processing.executor": "Run",
    "Python.obr_data_processing_CLEO_plot.executor": "Run",
    "Python.optics.executor": "Run",
    "Python.part_d.executor": "Run",
    "Python.passive_assembly_plots.executor": "Run",
    "Python.plot.executor": "Run",
    "Python.plot_2D.executor": "Run",
    "Python.plot_BER_over_channel.executor": "Run",
    "Python.plot_EDWA_spectrum.executor": "Run",
    "Python.plot_IQ_vector.executor": "Run",
    "Python.plot_PI_manual.executor": "Run",
    "Python.plot_PN.executor": "Run",
    "Python.plot_PN_from_IQ_vectors.executor": "Run",
    "Python.plot_QDMLLD_spectrum.executor": "Run",
    "Python.plot_RF_phase_noise.executor": "Run",
    "Python.plot_RF_spectrum.executor": "Run",
    "Python.plot_data.executor": "Run",
    "Python.plot_lateral_offset_tolerance.executor": "Run",
    "Python.plot_results.executor": "Run",
    "Python.plot_spectrum.executor": "Run",
    "Python.plot_sweep.executor": "Run",
    "Python.plot_trajectory.executor": "Run",
    "Python.plots.executor": "Run",
    "Python.pm103Scope.executor": "Run",
    "Python.pml (1).executor": "Run",
    "Python.pml.executor": "Run",
    "Python.polarization_measurement.executor": "Run",
    "Python.polynomial_surf.executor": "Run",
    "Python.prism.executor": "Run",
    "Python.prism_angles_opt.executor": "Run",
    "Python.prism_array.executor": "Run",
    "Python.prism_design (1).executor": "Run",
    "Python.prism_design.executor": "Run",
    "Python.propagate_imported_field.executor": "Debug",
    "Python.propagate_measured_mf.executor": "Run",
    "Python.pump_die_to_EDWA.executor": "Run",
    "Python.pump_lens (1).executor": "Run",
    "Python.pump_lens.executor": "Run",
    "Python.read_data.executor": "Run",
    "Python.reflectivity_characterization.executor": "Run",
    "Python.repair_mesh.executor": "Run",
    "Python.repaire_mesh.executor": "Run",
    "Python.result_analyser.executor": "Debug",
    "Python.reversed_path_no_prism.executor": "Run",
    "Python.reversed_path_optimized (1).executor": "Run",
    "Python.reversed_path_optimized.executor": "Run",
    "Python.send_mails.executor": "Run",
    "Python.sim_helpers.executor": "Run",
    "Python.simple_lens_with_imported_field.executor": "Run",
    "Python.simple_script.executor": "Run",
    "Python.singleChanScope.executor": "Debug",
    "Python.single_CCS_low_noise (1).executor": "Run",
    "Python.slicing.executor": "Debug",
    "Python.smc100 (1).executor": "Run",
    "Python.smc100.executor": "Run",
    "Python.split_model.executor": "Run",
    "Python.start.executor": "Run",
    "Python.stl2depth.executor": "Run",
    "Python.straight_EDWA_lens (1).executor": "Run",
    "Python.straight_EDWA_lens.executor": "Run",
    "Python.straight_EDWA_lens_default_mfd_fit.executor": "Run",
    "Python.straight_EDWA_lens_gaussian_mfd_fit.executor": "Run",
    "Python.straight_lens_gaussian_mfd_fit.executor": "Run",
    "Python.straight_pump_lens (1).executor": "Run",
    "Python.straight_pump_lens.executor": "Run",
    "Python.straight_pump_lens_temp.executor": "Run",
    "Python.straight_pump_to_EDWA.executor": "Run",
    "Python.structures.executor": "Run",
    "Python.surfaces.executor": "Run",
    "Python.sweep_b_d (1).executor": "Run",
    "Python.sweep_b_d.executor": "Debug",
    "Python.sweep_b_d_imported_field.executor": "Run",
    "Python.sweep_b_d_old.executor": "Debug",
    "Python.sweep_finer.executor": "Debug",
    "Python.temp (1).executor": "Run",
    "Python.temp.executor": "Run",
    "Python.test (1).executor": "Run",
    "Python.test.executor": "Run",
    "Python.test_pml.executor": "Run",
    "Python.test_pyfocus.executor": "Run",
    "Python.thorlab_index_matching_gel.executor": "Run",
    "Python.tilt_plate.executor": "Run",
    "Python.tilted_slab_test (1).executor": "Run",
    "Python.tilted_slab_test.executor": "Run",
    "Python.tilted_slab_test_optimizer3.executor": "Run",
    "Python.tilted_surface_test.executor": "Run",
    "Python.unit_converter.executor": "Run",
    "Python.utils.executor": "Run",
    "Python.view_results.executor": "Run",
    "Python.visualize_mesh.executor": "Run",
    "Python.wavelength_power_sweep.executor": "Run",
    "Python.waveshaper_wrapper.executor": "Run",
    "Python.with_GUI.executor": "Run",
    "Python.wpms (1).executor": "Run",
    "Python.wpms (2).executor": "Run",
    "Python.wpms (3).executor": "Debug",
    "Python.wpms.executor": "Run",
    "Python.wpms_TE.executor": "Debug",
    "Python.wpms_dup.executor": "Debug",
    "Python.wpms_temp.executor": "Run",
    "Python.wsp_generator (1).executor": "Run",
    "Python.wsp_generator.executor": "Run",
    "Python.x_straight_EDWA_lens.executor": "Run",
    "Python.x_straight_pump_lens (1).executor": "Run",
    "Python.x_straight_pump_lens.executor": "Run",
    "Python.y_straight_EDWA_lens.executor": "Run",
    "Python.y_straight_pump_lens (1).executor": "Run",
    "Python.y_straight_pump_lens.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "main",
    "last_opened_file_path": "C:/Users/<USER>/Documents/PycharmProject/ripple/scripts/MAGNIFY/D211_large_mfd/F3_doped_26cm_opt_for_1550_SQS",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "run.code.analysis.last.selected.profile": "aDefault",
    "settings.editor.selected.configurable": "PyScientificConfigurable",
    "two.files.diff.last.used.file": "C:/Users/<USER>/Documents/PycharmProject/device_wrappers/Keysight_B2902A_driver/B2902A_wrapper.py",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="PyConsoleOptionsProvider">
    <option name="myPythonConsoleState">
      <console-settings module-name="PycharmProject" is-module-sdk="true">
        <option name="myUseModuleSdk" value="true" />
        <option name="myModuleName" value="PycharmProject" />
      </console-settings>
    </option>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Documents\PycharmProject\ripple\scripts\MAGNIFY\D211_large_mfd\F3_doped_26cm_opt_for_1550_SQS" />
      <recent name="C:\Users\<USER>\Documents\PycharmProject\ripple\scripts\MAGNIFY\D143_LGT\C9_F6_opt_for_1550_gaussian" />
      <recent name="C:\Users\<USER>\Documents\PycharmProject\ripple\scripts\MAGNIFY\D143_LGT" />
      <recent name="C:\Users\<USER>\Documents\PycharmProject\ripple\scripts\MAGNIFY\D211_large_mfd\F1_doped_26cm_opt_for_1550_SQS" />
      <recent name="C:\Users\<USER>\Documents\PycharmProject\measurement\MFD_measurement\devices" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Documents\PycharmProject\measurement\EDWA_characterization\Gain" />
      <recent name="C:\Users\<USER>\Documents\PycharmProject\measurement\EDWA_characterization\Facet_reflection" />
      <recent name="C:\Users\<USER>\Documents\PycharmProject\measurement\EDWA_characterization\EPFL_ref" />
      <recent name="C:\Users\<USER>\Documents\PycharmProject\measurement\EDWA_characterization" />
      <recent name="C:\Users\<USER>\Documents\PycharmProject\ripple\scripts\MAGNIFY\D211_large_mfd\propagate_measurement" />
    </key>
  </component>
  <component name="RunManager" selected="Python.IPQ-THSensors">
    <configuration name="01_straight_EDWA_lens_single_fit" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="PycharmProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F3_doped_26cm_opt_for_1550_SQS" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F3_doped_26cm_opt_for_1550_SQS/01_straight_EDWA_lens_single_fit.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="04_angled_7deg_EDWA_to_fiber" type="PythonConfigurationType" factoryName="Python" singleton="false" nameIsGenerated="true">
      <module name="PycharmProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D174_01" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D174_01/opt_for_1550/04_angled_7deg_EDWA_to_fiber.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="B2902A_ai" type="PythonConfigurationType" factoryName="Python">
      <module name="PycharmProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="mfd-meas" />
      <option name="WORKING_DIRECTORY" value="C:\Users\<USER>\Documents\PycharmProject\device_wrappers" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/device_wrappers/Keysight_B2902A_driver/B2902A_wrapper_ai.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="IPQ-THSensors" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="PycharmProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/myutils" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/myutils/IPQ-THSensors.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="Th14" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="PycharmProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/myutils" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/myutils/Th14.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="build_straight_lens_side_print" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="PycharmProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/ripple/Inventor" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/ripple/Inventor/build_straight_lens_side_print.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="export_EDWA_lens_straight" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="PycharmProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F3_doped_26cm_opt_for_1550_SQS" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F3_doped_26cm_opt_for_1550_SQS/export_EDWA_lens_straight.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test_pyfocus" type="PythonConfigurationType" factoryName="Python">
      <module name="PycharmProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/PyFocus/src/test_pyfocus.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Python.04_angled_7deg_EDWA_to_fiber" />
      <item itemvalue="Python.B2902A_ai" />
      <item itemvalue="Python.test_pyfocus" />
      <item itemvalue="Python.01_straight_EDWA_lens_single_fit" />
      <item itemvalue="Python.IPQ-THSensors" />
      <item itemvalue="Python.build_straight_lens_side_print" />
      <item itemvalue="Python.export_EDWA_lens_straight" />
      <item itemvalue="Python.Th14" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.IPQ-THSensors" />
        <item itemvalue="Python.build_straight_lens_side_print" />
        <item itemvalue="Python.export_EDWA_lens_straight" />
        <item itemvalue="Python.01_straight_EDWA_lens_single_fit" />
        <item itemvalue="Python.Th14" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-PY-243.25659.43" />
        <option value="bundled-python-sdk-181015f7ab06-4df51de95216-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-243.25659.43" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SshConsoleOptionsProvider">
    <option name="myEncoding" value="UTF-8" />
    <option name="myConnectionType" value="NONE" />
    <option name="myConnectionId" value="" />
  </component>
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="4fb1c135-49ea-4a13-b79b-3ec883d52ffa" name="Changes" comment="" />
      <created>1721135010137</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1721135010137</updated>
      <workItem from="1721135011224" duration="2115000" />
      <workItem from="1721138529907" duration="15192000" />
      <workItem from="1721200621575" duration="15607000" />
      <workItem from="1721237403703" duration="1666000" />
      <workItem from="1721239284688" duration="15291000" />
      <workItem from="1721388326924" duration="6894000" />
      <workItem from="1721404329124" duration="1060000" />
      <workItem from="1721405827772" duration="583000" />
      <workItem from="1721406423375" duration="171000" />
      <workItem from="1721406601641" duration="54000" />
      <workItem from="1721406662760" duration="2878000" />
      <workItem from="1721632260268" duration="35939000" />
      <workItem from="1721812644869" duration="34558000" />
      <workItem from="1722848437571" duration="10689000" />
      <workItem from="1722867193014" duration="57598000" />
      <workItem from="1723037519060" duration="12281000" />
      <workItem from="1723101317868" duration="45308000" />
      <workItem from="1723219568797" duration="26294000" />
      <workItem from="1723473119941" duration="2960000" />
      <workItem from="1723476090744" duration="215000" />
      <workItem from="1723476439395" duration="32214000" />
      <workItem from="1723564015955" duration="3865000" />
      <workItem from="1723619958833" duration="35000" />
      <workItem from="1723620070875" duration="256000" />
      <workItem from="1723620665947" duration="42210000" />
      <workItem from="1723794112331" duration="155000" />
      <workItem from="1723794274528" duration="1638000" />
      <workItem from="1723797158517" duration="23468000" />
      <workItem from="1723826375425" duration="89275000" />
      <workItem from="1724247334258" duration="46621000" />
      <workItem from="1724398585338" duration="72666000" />
      <workItem from="1724832327369" duration="82139000" />
      <workItem from="1725260005394" duration="428000" />
      <workItem from="1725260637418" duration="145542000" />
      <workItem from="1725969559877" duration="1100000" />
      <workItem from="1725970666520" duration="2064000" />
      <workItem from="1725972744600" duration="2894000" />
      <workItem from="1725975651111" duration="40849000" />
      <workItem from="1726070869658" duration="2398000" />
      <workItem from="1726126464445" duration="4656000" />
      <workItem from="1726131206350" duration="160059000" />
      <workItem from="1727282607366" duration="41245000" />
      <workItem from="1727705328784" duration="31937000" />
      <workItem from="1727890758801" duration="72075000" />
      <workItem from="1728461655525" duration="20679000" />
      <workItem from="1728543939031" duration="34225000" />
      <workItem from="1728643863798" duration="4001000" />
      <workItem from="1728896820874" duration="48118000" />
      <workItem from="1729014404266" duration="4219000" />
      <workItem from="1729018648194" duration="197000" />
      <workItem from="1729018940140" duration="620000" />
      <workItem from="1729065017855" duration="8254000" />
      <workItem from="1729075242176" duration="12047000" />
      <workItem from="1729088192942" duration="28058000" />
      <workItem from="1729174644416" duration="74680000" />
      <workItem from="1729672541389" duration="6300000" />
      <workItem from="1729679220519" duration="87066000" />
      <workItem from="1730740074088" duration="55475000" />
      <workItem from="1730910048443" duration="100648000" />
      <workItem from="1731601101639" duration="59431000" />
      <workItem from="1732005011489" duration="2288000" />
      <workItem from="1732034928964" duration="116431000" />
      <workItem from="1732810421802" duration="1092000" />
      <workItem from="1732811681196" duration="4540000" />
      <workItem from="1732817709572" duration="39956000" />
      <workItem from="1732973601211" duration="2691000" />
      <workItem from="1732976702845" duration="82899000" />
      <workItem from="1733389540274" duration="1809000" />
      <workItem from="1733392120148" duration="12770000" />
      <workItem from="1733474171357" duration="19110000" />
      <workItem from="1733908045691" duration="24258000" />
      <workItem from="1733995378855" duration="39691000" />
      <workItem from="1734343484957" duration="4599000" />
      <workItem from="1734350653429" duration="89614000" />
      <workItem from="1737966231707" duration="4525000" />
      <workItem from="1737976921779" duration="21703000" />
      <workItem from="1738070270343" duration="5795000" />
      <workItem from="1738227343667" duration="76428000" />
      <workItem from="1738936170182" duration="3960000" />
      <workItem from="1739199018555" duration="24054000" />
      <workItem from="1739362172475" duration="4888000" />
      <workItem from="1739534739773" duration="25604000" />
      <workItem from="1739955643981" duration="29828000" />
      <workItem from="1740139761742" duration="5638000" />
      <workItem from="1740406527720" duration="66336000" />
      <workItem from="1740991174626" duration="10244000" />
      <workItem from="1741008177631" duration="396000" />
      <workItem from="1741008580973" duration="840000" />
      <workItem from="1741009428821" duration="16760000" />
      <workItem from="1741195442563" duration="41971000" />
      <workItem from="1741681828176" duration="35759000" />
      <workItem from="1741884071124" duration="836000" />
      <workItem from="1741939012155" duration="16408000" />
      <workItem from="1742213826008" duration="2211000" />
      <workItem from="1742217256961" duration="3210000" />
      <workItem from="1742220479147" duration="6561000" />
      <workItem from="1742227755266" duration="59576000" />
      <workItem from="1743402951134" duration="1310000" />
      <workItem from="1743409488855" duration="18179000" />
      <workItem from="1743492416168" duration="4474000" />
      <workItem from="1743587212630" duration="5070000" />
      <workItem from="1743667755425" duration="10380000" />
      <workItem from="1744013888578" duration="1913000" />
      <workItem from="1744015834065" duration="14089000" />
      <workItem from="1744285257248" duration="10995000" />
      <workItem from="1744618259682" duration="30478000" />
      <workItem from="1745155221768" duration="4440000" />
      <workItem from="1745404168982" duration="10251000" />
      <workItem from="1745934970103" duration="780000" />
      <workItem from="1746182724486" duration="3679000" />
      <workItem from="1746199789234" duration="1055000" />
      <workItem from="1746258429009" duration="1630000" />
      <workItem from="1747309746194" duration="13037000" />
      <workItem from="1747387411070" duration="42732000" />
      <workItem from="1747728912745" duration="13764000" />
      <workItem from="1747827720522" duration="458000" />
      <workItem from="1747915833236" duration="70424000" />
      <workItem from="1748442062827" duration="27842000" />
      <workItem from="1748613948524" duration="725000" />
      <workItem from="1748614679476" duration="690000" />
      <workItem from="1748615570834" duration="10828000" />
      <workItem from="1749042519340" duration="4467000" />
      <workItem from="1749133188536" duration="8117000" />
      <workItem from="1749629225746" duration="6984000" />
      <workItem from="1749669004043" duration="5109000" />
      <workItem from="1749731830915" duration="17415000" />
      <workItem from="1750005370181" duration="694000" />
      <workItem from="1750006314819" duration="9130000" />
      <workItem from="1750083102686" duration="9791000" />
      <workItem from="1750094283180" duration="16423000" />
      <workItem from="1750253226875" duration="28883000" />
      <workItem from="1750336619671" duration="2228000" />
      <workItem from="1750404317362" duration="50659000" />
      <workItem from="1750670104284" duration="7774000" />
      <workItem from="1750685478243" duration="53366000" />
      <workItem from="1751014618686" duration="63758000" />
      <workItem from="1751356900122" duration="13696000" />
      <workItem from="1751387380946" duration="9312000" />
      <workItem from="1751451761484" duration="74140000" />
      <workItem from="1752052773370" duration="67297000" />
      <workItem from="1752729932312" duration="111897000" />
      <workItem from="1753278302378" duration="5477000" />
      <workItem from="1753290666384" duration="291499000" />
      <workItem from="1755071139597" duration="9216000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName="*.m" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/wave_propagation_method/optical_elements2/surfaces/implementations.py</url>
          <line>169</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/wave_propagation_method/optical_elements2/surfaces/implementations.py</url>
          <line>170</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/wpm_mb/pml.py</url>
          <line>67</line>
          <option name="timeStamp" value="31" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/wave_propagation_method/lens_optimizer_multilens2.py</url>
          <line>490</line>
          <option name="timeStamp" value="92" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/PyFocus/src/test_pyfocus.py</url>
          <line>55</line>
          <option name="timeStamp" value="324" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/PyFocus/src/PyFocus/model/main_calculation_handler.py</url>
          <line>97</line>
          <option name="timeStamp" value="325" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$USER_HOME$/AppData/Local/anaconda3/envs/diffractio/Lib/site-packages/diffractio/scalar_fields_XZ.py</url>
          <line>981</line>
          <option name="timeStamp" value="396" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/diffractio/diffractio/scalar_fields_XY.py</url>
          <line>2827</line>
          <option name="timeStamp" value="397" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/diffractio/diffractio/scalar_fields_XY.py</url>
          <line>2818</line>
          <option name="timeStamp" value="398" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/diffractio/diffractio/vector_fields_XZ.py</url>
          <line>1551</line>
          <option name="timeStamp" value="399" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/diffractio/diffractio/vector_fields_XZ.py</url>
          <line>1507</line>
          <option name="timeStamp" value="400" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/src/wpms.py</url>
          <line>20</line>
          <option name="timeStamp" value="441" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/wpms.py</url>
          <line>1045</line>
          <option name="timeStamp" value="512" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/wpms.py</url>
          <line>905</line>
          <option name="timeStamp" value="531" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/lumpy/analyzation/result_analyser.py</url>
          <line>187</line>
          <option name="timeStamp" value="547" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/QD_MLLD_to_SiN/10deg/QD_MLLD_lens.py</url>
          <line>44</line>
          <option name="timeStamp" value="583" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/QD_MLLD_to_SiN/10deg/SiN_lens.py</url>
          <line>42</line>
          <option name="timeStamp" value="584" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/QD_MLLD_to_SiN/20deg/QDMLLD_to_SiN_d4.py</url>
          <line>82</line>
          <option name="timeStamp" value="606" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/process_measurement/MFD_setup/mfd_fit/mfd_fit.py</url>
          <line>68</line>
          <option name="timeStamp" value="610" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/MAGNIFY/D143_w_WDM/pump_to_EDWA/angled/5deg_EDWA_lens.py</url>
          <line>70</line>
          <option name="timeStamp" value="615" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://I:/HOME/wiss/WISSWORK/HybridCombs/LIV_measuremet_python_project/LIV_measurement.py</url>
          <line>63</line>
          <option name="timeStamp" value="618" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://I:/HOME/wiss/WISSWORK/HybridCombs/LIV_measuremet_python_project/LIV_measurement.py</url>
          <line>77</line>
          <option name="timeStamp" value="619" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://I:/HOME/wiss/WISSWORK/HybridCombs/LIV_measuremet_python_project/LIV_measurement.py</url>
          <line>106</line>
          <option name="timeStamp" value="620" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://I:/HOME/wiss/WISSWORK/HybridCombs/LIV_measuremet_python_project/LIV_measurement.py</url>
          <line>91</line>
          <option name="timeStamp" value="622" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/QD_MLLD_to_SiN/01_QD_MLLD_to_SiN.py</url>
          <line>107</line>
          <option name="timeStamp" value="628" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/QD_MLLD_to_SiN/01_QD_MLLD_to_SiN.py</url>
          <line>60</line>
          <option name="timeStamp" value="643" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/analysis/coupling_axial_offset.py</url>
          <line>59</line>
          <option name="timeStamp" value="662" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/OPALID_Cyl/sweep_ellipsoid_lens/sweep_b_d.py</url>
          <line>76</line>
          <option name="timeStamp" value="695" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/structures.py</url>
          <line>1002</line>
          <option name="timeStamp" value="743" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/device_wrappers/Light_Analysis_Examples/Python/Thorlabs PMxxx Power Meters/scpi/scopeMode/pm103Scope.py</url>
          <line>203</line>
          <option name="timeStamp" value="754" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/device_wrappers/Light_Analysis_Examples/Python/Thorlabs PMxxx Power Meters/Obsolete/PMxxx using ctypes - Python 3.py</url>
          <line>35</line>
          <option name="timeStamp" value="756" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/device_wrappers/AeroDIODE/single_CCS_low_noise.py</url>
          <line>6</line>
          <option name="timeStamp" value="766" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>jar://$USER_HOME$/AppData/Local/anaconda3/envs/mfd-meas/Lib/site-packages/aerodiode-1.1.0-py3.12.egg!/aerodiode/pdmv5_cw.py</url>
          <line>154</line>
          <option name="timeStamp" value="774" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>jar://$USER_HOME$/AppData/Local/anaconda3/envs/mfd-meas/Lib/site-packages/aerodiode-1.1.0-py3.12.egg!/aerodiode/pdmv5_cw.py</url>
          <line>152</line>
          <option name="timeStamp" value="775" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>jar://$USER_HOME$/AppData/Local/anaconda3/envs/mfd-meas/Lib/site-packages/aerodiode-1.1.0-py3.12.egg!/aerodiode/pdmv5_cw.py</url>
          <line>150</line>
          <option name="timeStamp" value="777" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>jar://$USER_HOME$/AppData/Local/anaconda3/envs/mfd-meas/Lib/site-packages/aerodiode-1.1.0-py3.12.egg!/aerodiode/pdmv5_cw.py</url>
          <line>144</line>
          <option name="timeStamp" value="778" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>jar://$USER_HOME$/AppData/Local/anaconda3/envs/mfd-meas/Lib/site-packages/aerodiode-1.1.0-py3.12.egg!/aerodiode/pdmv5_cw.py</url>
          <line>145</line>
          <option name="timeStamp" value="779" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/measurement/laser_characterization/old/LIV_measurement.py</url>
          <line>141</line>
          <option name="timeStamp" value="781" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/THz_lens/concave_imported_source.py</url>
          <line>13</line>
          <option name="timeStamp" value="782" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/device_wrappers/Newfocus_TLB6700/control_TLB6700.py</url>
          <line>15</line>
          <option name="timeStamp" value="784" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/Optical_communication_101/QAM.py</url>
          <line>17</line>
          <option name="timeStamp" value="785" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/Optical_communication_101/constellation_diagram.py</url>
          <line>61</line>
          <option name="timeStamp" value="786" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/process_measurement/PNA/plot_RF_phase_noise.py</url>
          <line>24</line>
          <option name="timeStamp" value="787" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/OPALID_Cyl/simple_lens_with_imported_field.py</url>
          <line>47</line>
          <option name="timeStamp" value="788" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/MAGNIFY/D174_01/opt_for_1550/03_angled_7deg_lens_on_fiber.py</url>
          <line>37</line>
          <option name="timeStamp" value="825" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/MAGNIFY/D174_01/opt_for_1550/02_angled_7deg_lens_on_EDWA_gaussian_mfd_fit.py</url>
          <line>37</line>
          <option name="timeStamp" value="826" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/analysis/coupling_translation_offset.py</url>
          <line>36</line>
          <option name="timeStamp" value="827" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/OPALID_Cyl/sweep_ellipsoid_lens/sweep_b_d_imported_field.py</url>
          <line>83</line>
          <option name="timeStamp" value="832" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/OPALID_Cyl/sweep_ellipsoid_lens/sweep_b_d_imported_field.py</url>
          <line>53</line>
          <option name="timeStamp" value="833" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/Inventor/build_straight_lens_top_print_Sonata.py</url>
          <line>85</line>
          <option name="timeStamp" value="834" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/device_wrappers/Thorlabs_VOA_DV1550AA/DV1550AA_wrapper.py</url>
          <line>115</line>
          <option name="timeStamp" value="848" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/device_wrappers/Thorlabs_VOA_DV1550AA/DV1550AA_wrapper.py</url>
          <line>114</line>
          <option name="timeStamp" value="849" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/QD_MLLD_to_SiN/CLEO_pair/01_QD_MLLD_to_SiN.py</url>
          <line>61</line>
          <option name="timeStamp" value="856" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/measurement/MFD_measurement/devices/EPFL_D143_LGT_F2.py</url>
          <line>19</line>
          <option name="timeStamp" value="863" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/device_wrappers/Keysight_B2902A_driver/B2902A_wrapper.py</url>
          <line>327</line>
          <option name="timeStamp" value="864" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/myutils/images_to_video.py</url>
          <line>119</line>
          <option name="timeStamp" value="865" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/coupling_optimizer.py</url>
          <line>183</line>
          <option name="timeStamp" value="870" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/field.py</url>
          <line>265</line>
          <option name="timeStamp" value="871" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/OPALID_Cyl/propagate_imported_field.py</url>
          <line>33</line>
          <option name="timeStamp" value="873" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/propagate_measurement/propagate_measured_mf.py</url>
          <line>34</line>
          <option name="timeStamp" value="875" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/measurement/EDWA_characterization/EPFL_ref/Figure_S13_chip_facet_reflectivity.py</url>
          <line>148</line>
          <option name="timeStamp" value="881" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/measurement/EDWA_characterization/Facet_reflection/Extract_facet_reflection.py</url>
          <line>52</line>
          <option name="timeStamp" value="885" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/measurement/EDWA_characterization/Facet_reflection/Extract_facet_reflection.py</url>
          <line>33</line>
          <option name="timeStamp" value="887" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1480_vlink/05_8deg_EDWA_to_fiber_used.py</url>
          <line>37</line>
          <option name="timeStamp" value="890" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F3_doped_26cm_opt_for_1550_SQS/04_7deg_fiber_lens.py</url>
          <line>34</line>
          <option name="timeStamp" value="891" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F3_doped_26cm_opt_for_1550_SQS/03_7deg_EDWA_lens_single_fit.py</url>
          <line>35</line>
          <option name="timeStamp" value="893" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/measurement/MFD_measurement/devices/VLINK_16ch_pitch127um_lidless_fiberarray_with_lens.py</url>
          <line>15</line>
          <option name="timeStamp" value="896" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F3_doped_26cm_opt_for_1550_SQS/05_8deg_EDWA_to_fiber.py</url>
          <line>37</line>
          <option name="timeStamp" value="897" />
        </line-breakpoint>
      </breakpoints>
      <default-breakpoints>
        <breakpoint type="python-exception">
          <properties notifyOnTerminate="true" exception="BaseException">
            <option name="notifyOnTerminate" value="true" />
          </properties>
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="PythonConfigurationType">
        <watch expression="k_e" />
        <watch expression="temp" />
      </configuration>
    </watches-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/PycharmProject$SiC_1560_30mfd_output.coverage" NAME="SiC_1560_30mfd_output Coverage Results" MODIFIED="1739273990784" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/DTU" />
    <SUITE FILE_PATH="coverage/PycharmProject$Multilane_C5_straight_tapers.coverage" NAME="Multilane_C5_straight_tapers Coverage Results" MODIFIED="1731052317388" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$wsp_generator.coverage" NAME="wsp_generator Coverage Results" MODIFIED="1753110130622" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/II_VI_waveshaper" />
    <SUITE FILE_PATH="coverage/PycharmProject$angled_7deg_lens_fiber.coverage" NAME="angled_7deg_lens_fiber Coverage Results" MODIFIED="1748597334800" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D174_01" />
    <SUITE FILE_PATH="coverage/PycharmProject$plot_PN_from_IQ_vectors.coverage" NAME="plot_PN_from_IQ_vectors Coverage Results" MODIFIED="1753171352816" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/process_measurement/PNA" />
    <SUITE FILE_PATH="coverage/PycharmProject$GUI_10kHz.coverage" NAME="GUI_10kHz Coverage Results" MODIFIED="1738765105730" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Thorlab_PMxxx" />
    <SUITE FILE_PATH="coverage/PycharmProject$pm103Scope.coverage" NAME="pm103Scope Coverage Results" MODIFIED="1738239689038" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Light_Analysis_Examples/Python/Thorlabs PMxxx Power Meters/scpi/scopeMode" />
    <SUITE FILE_PATH="coverage/PycharmProject$thorlab_index_matching_gel.coverage" NAME="thorlab_index_matching_gel Coverage Results" MODIFIED="1747822732498" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/misc" />
    <SUITE FILE_PATH="coverage/PycharmProject$oblique_interface__1_.coverage" NAME="oblique_interface (1) Coverage Results" MODIFIED="1723628781291" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Debug/vwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$reversed_path_no_prism.coverage" NAME="reversed_path_no_prism Coverage Results" MODIFIED="1727795564685" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wave_propagation_method/designs/Hybrid_combs_packaging/low_backreflection" />
    <SUITE FILE_PATH="coverage/PycharmProject$tilted_surface_test.coverage" NAME="tilted_surface_test Coverage Results" MODIFIED="1724762787112" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wave_propagation_method/designs/tilted_slab_test" />
    <SUITE FILE_PATH="coverage/PycharmProject$tilt_plate.coverage" NAME="tilt_plate Coverage Results" MODIFIED="1729268692182" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN/20deg" />
    <SUITE FILE_PATH="coverage/PycharmProject$04_7deg_fiber_lens.coverage" NAME="04_7deg_fiber_lens Coverage Results" MODIFIED="1754376272811" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550_SQS" />
    <SUITE FILE_PATH="coverage/PycharmProject$surfaces.coverage" NAME="surfaces Coverage Results" MODIFIED="1741595811641" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple" />
    <SUITE FILE_PATH="coverage/PycharmProject$field.coverage" NAME="field Coverage Results" MODIFIED="1748444371414" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple" />
    <SUITE FILE_PATH="coverage/PycharmProject$angled_7deg_lens_gaussian_mfd_fit.coverage" NAME="angled_7deg_lens_gaussian_mfd_fit Coverage Results" MODIFIED="1748597343721" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D174_01" />
    <SUITE FILE_PATH="coverage/PycharmProject$lens_optimizer_multilens3_plane_wave_paper_figure.coverage" NAME="lens_optimizer_multilens3_plane_wave_paper_figure Coverage Results" MODIFIED="1733922714645" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wave_propagation_method/SNSPD" />
    <SUITE FILE_PATH="coverage/PycharmProject$01_QD_MLLD_to_SiN_prism_v2__1_.coverage" NAME="01_QD_MLLD_to_SiN_prism_v2 (1) Coverage Results" MODIFIED="1728302476517" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN" />
    <SUITE FILE_PATH="coverage/PycharmProject$build_SiN_lens.coverage" NAME="build_SiN_lens Coverage Results" MODIFIED="1731849667323" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/Inventor" />
    <SUITE FILE_PATH="coverage/PycharmProject$fastMode.coverage" NAME="fastMode Coverage Results" MODIFIED="1738239769182" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Light_Analysis_Examples/Python/Thorlabs PMxxx Power Meters/scpi/fastMode" />
    <SUITE FILE_PATH="coverage/PycharmProject$ERC.coverage" NAME="ERC Coverage Results" MODIFIED="1725614763617" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Others" />
    <SUITE FILE_PATH="coverage/PycharmProject$straight_EDWA_lens_gaussian_mfd_fit.coverage" NAME="straight_EDWA_lens_gaussian_mfd_fit Coverage Results" MODIFIED="1732637717251" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/EDWA_to_fiber" />
    <SUITE FILE_PATH="coverage/PycharmProject$QPSK.coverage" NAME="QPSK Coverage Results" MODIFIED="1744023160477" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Optical_communication_101" />
    <SUITE FILE_PATH="coverage/PycharmProject$EPFL_D213_doped_17_22_27cm.coverage" NAME="EPFL_D213_doped_17-22-27cm Coverage Results" MODIFIED="1752848482543" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$hybrid_comb_lens.coverage" NAME="hybrid_comb_lens Coverage Results" MODIFIED="1728303503517" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/lumpy/scripts" />
    <SUITE FILE_PATH="coverage/PycharmProject$SiN_lens__1_.coverage" NAME="SiN_lens (1) Coverage Results" MODIFIED="1728409145884" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN/10deg" />
    <SUITE FILE_PATH="coverage/PycharmProject$SemiNex_pump_186_2_with_lens.coverage" NAME="SemiNex_pump_186-2_with_lens Coverage Results" MODIFIED="1732047385116" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$04_angled_7deg_EDWA_to_fiber.coverage" NAME="04_angled_7deg_EDWA_to_fiber Coverage Results" MODIFIED="1751395459212" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D174_01" />
    <SUITE FILE_PATH="coverage/PycharmProject$EPFL_SiN_550nm_taper.coverage" NAME="EPFL_SiN_550nm_taper Coverage Results" MODIFIED="1728916038673" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$AMF_MFD40.coverage" NAME="AMF_MFD40 Coverage Results" MODIFIED="1731054431570" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/EDWA_to_fiber" />
    <SUITE FILE_PATH="coverage/PycharmProject$singleChanScope.coverage" NAME="singleChanScope Coverage Results" MODIFIED="1738242290103" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Light_Analysis_Examples/Python/Thorlabs PMxxx Power Meters/scpi/scopeMode" />
    <SUITE FILE_PATH="coverage/PycharmProject$Multilane_C5_taper1p05_0p55um.coverage" NAME="Multilane_C5_taper1p05_0p55um Coverage Results" MODIFIED="1730819670898" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$plot_results.coverage" NAME="plot_results Coverage Results" MODIFIED="1725020385579" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Debug/vwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$QDMLLD_to_SiN_d4__1_.coverage" NAME="QDMLLD_to_SiN_d4 (1) Coverage Results" MODIFIED="1729786110394" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN/20deg" />
    <SUITE FILE_PATH="coverage/PycharmProject$imported_lens_single_excitation.coverage" NAME="imported_lens_single_excitation Coverage Results" MODIFIED="1738921065876" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/OPALID_Cyl" />
    <SUITE FILE_PATH="coverage/PycharmProject$MFD_100.coverage" NAME="MFD_100 Coverage Results" MODIFIED="1729106594560" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/SMF_to_AMF" />
    <SUITE FILE_PATH="coverage/PycharmProject$AMF22_EC__1_.coverage" NAME="AMF22_EC (1) Coverage Results" MODIFIED="1727271002039" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement_processing/MFD_setup/mfd_fit" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_mesh_EDWA.coverage" NAME="export_mesh_EDWA Coverage Results" MODIFIED="1748614132157" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D174_01" />
    <SUITE FILE_PATH="coverage/PycharmProject$facet_reflection_characterization__1_.coverage" NAME="facet_reflection_characterization (1) Coverage Results" MODIFIED="1753433690727" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/EDWA_characterization/Facet_reflection" />
    <SUITE FILE_PATH="coverage/PycharmProject$Part_c.coverage" NAME="Part c Coverage Results" MODIFIED="1734436963710" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Teaching/ps04_student" />
    <SUITE FILE_PATH="coverage/PycharmProject$lena_has_returned.coverage" NAME="lena_has_returned Coverage Results" MODIFIED="1726479721102" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/mfd_measurement/client_server_test" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_pump_lens2.coverage" NAME="export_pump_lens2 Coverage Results" MODIFIED="1731848757572" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/pump_to_EDWA/straight" />
    <SUITE FILE_PATH="coverage/PycharmProject$wpms_dup.coverage" NAME="wpms_dup Coverage Results" MODIFIED="1724066311904" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_model_default_fit.coverage" NAME="export_model_default_fit Coverage Results" MODIFIED="1731084469141" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/EDWA_to_fiber" />
    <SUITE FILE_PATH="coverage/PycharmProject$02_straight_fiber_lens.coverage" NAME="02_straight_fiber_lens Coverage Results" MODIFIED="1754335747295" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550_SQS" />
    <SUITE FILE_PATH="coverage/PycharmProject$smc100.coverage" NAME="smc100 Coverage Results" MODIFIED="1748023947939" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/SMC100" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_model_QD_lens__1_.coverage" NAME="export_model_QD_lens (1) Coverage Results" MODIFIED="1727803603106" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wave_propagation_method/designs/Hybrid_combs_packaging/low_backreflection" />
    <SUITE FILE_PATH="coverage/PycharmProject$anti_backreflection_surface.coverage" NAME="anti-backreflection_surface Coverage Results" MODIFIED="1730983156434" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/functionalities" />
    <SUITE FILE_PATH="coverage/PycharmProject$pytest_in_test_TLB6700_py.coverage" NAME="pytest in test_TLB6700.py Coverage Results" MODIFIED="1742208514489" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Newfocus_TLB-6700" />
    <SUITE FILE_PATH="coverage/PycharmProject$reversed_path_optimized__1_.coverage" NAME="reversed_path_optimized (1) Coverage Results" MODIFIED="1742376843857" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wave_propagation_method/designs/Hybrid_combs_packaging/low_backreflection" />
    <SUITE FILE_PATH="coverage/PycharmProject$QD_MLLD_lens_dummy.coverage" NAME="QD_MLLD_lens_dummy Coverage Results" MODIFIED="1728037288623" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN" />
    <SUITE FILE_PATH="coverage/PycharmProject$Ligentec_refractive_index.coverage" NAME="Ligentec_refractive_index Coverage Results" MODIFIED="1721894437781" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/myutils" />
    <SUITE FILE_PATH="coverage/PycharmProject$learn_multiprocessing.coverage" NAME="learn_multiprocessing Coverage Results" MODIFIED="1739973601548" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Playground" />
    <SUITE FILE_PATH="coverage/PycharmProject$tilted_slab_test__1_.coverage" NAME="tilted_slab_test (1) Coverage Results" MODIFIED="1723474749422" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wave_propagation_method/designs/tilted_slab_test" />
    <SUITE FILE_PATH="coverage/PycharmProject$Ligentec_SiN_Ring_angled_facet.coverage" NAME="Ligentec_SiN_Ring_angled_facet Coverage Results" MODIFIED="1728589888439" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement" />
    <SUITE FILE_PATH="coverage/PycharmProject$HHI_QWMLLD_49p7_A2.coverage" NAME="HHI_QWMLLD_49p7_A2 Coverage Results" MODIFIED="1731051112079" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$test.coverage" NAME="test Coverage Results" MODIFIED="1724343760543" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Rotating_lidar_David" />
    <SUITE FILE_PATH="coverage/PycharmProject$05_7deg_EDWA_to_fiber_not_opt.coverage" NAME="05_7deg_EDWA_to_fiber_not_opt Coverage Results" MODIFIED="1754423034451" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550_SQS" />
    <SUITE FILE_PATH="coverage/PycharmProject$mfd40_single_surf.coverage" NAME="mfd40_single_surf Coverage Results" MODIFIED="1753088194731" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/Rotating_lidar_David" />
    <SUITE FILE_PATH="coverage/PycharmProject$move_cam.coverage" NAME="move_cam Coverage Results" MODIFIED="1754397354347" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/util" />
    <SUITE FILE_PATH="coverage/PycharmProject$load_modules.coverage" NAME="load_modules Coverage Results" MODIFIED="1728303325921" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/lumpy/utils" />
    <SUITE FILE_PATH="coverage/PycharmProject$main_v2.coverage" NAME="main_v2 Coverage Results" MODIFIED="1741010963813" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/LIV_YC" />
    <SUITE FILE_PATH="coverage/PycharmProject$PhotonX_DFB_1560_30mfd.coverage" NAME="PhotonX_DFB_1560_30mfd Coverage Results" MODIFIED="1739208887881" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/DTU" />
    <SUITE FILE_PATH="coverage/PycharmProject$plot_2D.coverage" NAME="plot_2D Coverage Results" MODIFIED="1724344357610" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/PyFocus/tests/basic_functioning" />
    <SUITE FILE_PATH="coverage/PycharmProject$QD_MLLD_lens_F1.coverage" NAME="QD_MLLD_lens_F1 Coverage Results" MODIFIED="1742374148698" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wave_propagation_method/designs/Hybrid_combs_packaging/in_plane/first_design_1550" />
    <SUITE FILE_PATH="coverage/PycharmProject$prism_design__1_.coverage" NAME="prism_design (1) Coverage Results" MODIFIED="1730189841293" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/functionalities" />
    <SUITE FILE_PATH="coverage/PycharmProject$loadfilter.coverage" NAME="loadfilter Coverage Results" MODIFIED="1750425587198" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/II-VI_waveshaper" />
    <SUITE FILE_PATH="coverage/PycharmProject$io.coverage" NAME="io Coverage Results" MODIFIED="1743169063994" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/utils" />
    <SUITE FILE_PATH="coverage/PycharmProject$B2902A_wrapper.coverage" NAME="B2902A_wrapper Coverage Results" MODIFIED="1751394823585" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Keysight_B2902A_driver" />
    <SUITE FILE_PATH="coverage/PycharmProject$compress_pdf.coverage" NAME="compress_pdf Coverage Results" MODIFIED="1734447059700" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Tools" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_model_SiN_lens_d4.coverage" NAME="export_model_SiN_lens_d4 Coverage Results" MODIFIED="1729786257038" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN/20deg" />
    <SUITE FILE_PATH="coverage/PycharmProject$Doctest_via_pytest_in_B2902A_wrapper_ai_py.coverage" NAME="Doctest via pytest in B2902A_wrapper_ai.py Coverage Results" MODIFIED="1751396160386" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Keysight_B2902A_driver" />
    <SUITE FILE_PATH="coverage/PycharmProject$cylinder_lens.coverage" NAME="cylinder_lens Coverage Results" MODIFIED="1721723688681" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/OPALID_Cyl" />
    <SUITE FILE_PATH="coverage/PycharmProject$QDMLLD_49p7_J5__1_.coverage" NAME="QDMLLD_49p7_J5 (1) Coverage Results" MODIFIED="1728911049559" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$22_straight_fiber_lens_SQS_25MFD.coverage" NAME="22_straight_fiber_lens_SQS_25MFD Coverage Results" MODIFIED="1754920410101" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D143_LGT/C9_F6_opt_for_1550_gaussian" />
    <SUITE FILE_PATH="coverage/PycharmProject$calibrate_lens.coverage" NAME="calibrate_lens Coverage Results" MODIFIED="1729079200577" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/Inventor" />
    <SUITE FILE_PATH="coverage/PycharmProject$EPFL_D213.coverage" NAME="EPFL_D213 Coverage Results" MODIFIED="1750336539014" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$QD_MLLD_lens.coverage" NAME="QD_MLLD_lens Coverage Results" MODIFIED="1742458674721" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/../Temp/wpm_lens_design/designs/Hybrid_combs_packaging/low_backreflection/debug" />
    <SUITE FILE_PATH="coverage/PycharmProject$MFD40_2surf.coverage" NAME="MFD40_2surf Coverage Results" MODIFIED="1731060917507" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/EDWA_to_fiber" />
    <SUITE FILE_PATH="coverage/PycharmProject$build_straight_lens_top_print_Sonata.coverage" NAME="build_straight_lens_top_print_Sonata Coverage Results" MODIFIED="1750014678740" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/Inventor" />
    <SUITE FILE_PATH="coverage/PycharmProject$cylinder_lens_cut__1_.coverage" NAME="cylinder_lens_cut (1) Coverage Results" MODIFIED="1725973413334" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm_temp/scripts/OPALID_Cyl" />
    <SUITE FILE_PATH="coverage/PycharmProject$concave_imported_source.coverage" NAME="concave_imported_source Coverage Results" MODIFIED="1725955790455" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/THz_lens" />
    <SUITE FILE_PATH="coverage/PycharmProject$SMF_to_AMF_EC_mohamed.coverage" NAME="SMF_to_AMF_EC_mohamed Coverage Results" MODIFIED="1729172194839" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/SMF_to_AMF" />
    <SUITE FILE_PATH="coverage/PycharmProject$jakovljevic_2594161_PS04_TM_code.coverage" NAME="jakovljevic_2594161_PS04_TM_code Coverage Results" MODIFIED="1734444955436" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Teaching/ps04_student" />
    <SUITE FILE_PATH="coverage/PycharmProject$03_7deg_EDWA_lens_single_fit__1_.coverage" NAME="03_7deg_EDWA_lens_single_fit (1) Coverage Results" MODIFIED="1753469297180" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550" />
    <SUITE FILE_PATH="coverage/PycharmProject$pump_lens.coverage" NAME="pump_lens Coverage Results" MODIFIED="1730913601111" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY" />
    <SUITE FILE_PATH="coverage/PycharmProject$03_7deg_EDWA_lens_d4sigma_fit__1_.coverage" NAME="03_7deg_EDWA_lens_d4sigma_fit (1) Coverage Results" MODIFIED="1751805399235" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D143_LGT/opt_for_1480" />
    <SUITE FILE_PATH="coverage/PycharmProject$lens_optimizer_multilens2_plane_wave.coverage" NAME="lens_optimizer_multilens2_plane_wave Coverage Results" MODIFIED="1733918504670" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/yilin_wpm-main/SNSPD" />
    <SUITE FILE_PATH="coverage/PycharmProject$EPFL_D174_01.coverage" NAME="EPFL_D174_01 Coverage Results" MODIFIED="1750334257298" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$oblique_interface.coverage" NAME="oblique_interface Coverage Results" MODIFIED="1725032650575" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Debug/vwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$lens_optimizer_multilens3.coverage" NAME="lens_optimizer_multilens3 Coverage Results" MODIFIED="1721639525504" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wave_propagation_method" />
    <SUITE FILE_PATH="coverage/PycharmProject$wpms_temp.coverage" NAME="wpms_temp Coverage Results" MODIFIED="1723632052658" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$cylinder_lens_cut.coverage" NAME="cylinder_lens_cut Coverage Results" MODIFIED="1726242190756" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/OPALID_Cyl" />
    <SUITE FILE_PATH="coverage/PycharmProject$PMxxx_using_ctypes___Python_3.coverage" NAME="PMxxx using ctypes - Python 3 Coverage Results" MODIFIED="1738239894205" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Light_Analysis_Examples/Python/Thorlabs PMxxx Power Meters/Obsolete" />
    <SUITE FILE_PATH="coverage/PycharmProject$monitors.coverage" NAME="monitors Coverage Results" MODIFIED="1729244144884" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$PMxxx_SCPI_pyvisa.coverage" NAME="PMxxx_SCPI_pyvisa Coverage Results" MODIFIED="1741349147847" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Thorlab_PMxxx" />
    <SUITE FILE_PATH="coverage/PycharmProject$goldeye_G034_TEC1__1_.coverage" NAME="goldeye_G034_TEC1 (1) Coverage Results" MODIFIED="1726591926612" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/mfd_measurement/old" />
    <SUITE FILE_PATH="coverage/PycharmProject$OWF_PS04.coverage" NAME="OWF_PS04 Coverage Results" MODIFIED="1734540089988" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Teaching" />
    <SUITE FILE_PATH="coverage/PycharmProject$FindDevices.coverage" NAME="FindDevices Coverage Results" MODIFIED="1727178712368" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/LIV_measuremet" />
    <SUITE FILE_PATH="coverage/PycharmProject$Ligentec_SiN_Sagnac_mirror.coverage" NAME="Ligentec_SiN_Sagnac_mirror Coverage Results" MODIFIED="1728488884127" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement" />
    <SUITE FILE_PATH="coverage/PycharmProject$10deg_pump_lens.coverage" NAME="10deg_pump_lens Coverage Results" MODIFIED="1730985865155" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/angled" />
    <SUITE FILE_PATH="coverage/PycharmProject$SiC_1560_30mfd_input.coverage" NAME="SiC_1560_30mfd_input Coverage Results" MODIFIED="1739280590175" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/DTU" />
    <SUITE FILE_PATH="coverage/PycharmProject$03_8deg_EDWA_lens_single_fit__1_.coverage" NAME="03_8deg_EDWA_lens_single_fit (1) Coverage Results" MODIFIED="1753469252100" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550" />
    <SUITE FILE_PATH="coverage/PycharmProject$straight_EDWA_lens.coverage" NAME="straight_EDWA_lens Coverage Results" MODIFIED="1730977077908" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/straight" />
    <SUITE FILE_PATH="coverage/PycharmProject$mfd_measurer__1_.coverage" NAME="mfd_measurer (1) Coverage Results" MODIFIED="1727364741707" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement" />
    <SUITE FILE_PATH="coverage/PycharmProject$plot.coverage" NAME="plot Coverage Results" MODIFIED="1732969824011" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/utils" />
    <SUITE FILE_PATH="coverage/PycharmProject$AMF_MFD50_2surf.coverage" NAME="AMF_MFD50_2surf Coverage Results" MODIFIED="1733408961644" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Patrick" />
    <SUITE FILE_PATH="coverage/PycharmProject$non_symmetrical_QAM.coverage" NAME="non-symmetrical_QAM Coverage Results" MODIFIED="1744027598412" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Optical_communication_101" />
    <SUITE FILE_PATH="coverage/PycharmProject$Doctest_via_pytest_for_B2902A_wrapper_ai_KeysightB2902A.coverage" NAME="Doctest via pytest for B2902A_wrapper_ai.KeysightB2902A Coverage Results" MODIFIED="1751395066696" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Keysight_B2902A_driver" />
    <SUITE FILE_PATH="coverage/PycharmProject$prism_array.coverage" NAME="prism_array Coverage Results" MODIFIED="1730190886035" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/Inventor" />
    <SUITE FILE_PATH="coverage/PycharmProject$cut_lens.coverage" NAME="cut_lens Coverage Results" MODIFIED="1729018245891" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Inventor" />
    <SUITE FILE_PATH="coverage/PycharmProject$build_fiber_lens_side_print.coverage" NAME="build_fiber_lens_side_print Coverage Results" MODIFIED="1754427174796" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/Inventor" />
    <SUITE FILE_PATH="coverage/PycharmProject$mfd50.coverage" NAME="mfd50 Coverage Results" MODIFIED="1754032595809" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/Rotating_lidar_David" />
    <SUITE FILE_PATH="coverage/PycharmProject$tilted_slab_test_optimizer3.coverage" NAME="tilted_slab_test_optimizer3 Coverage Results" MODIFIED="1723474351662" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/yilin_wpm-main/designs/tilted_slab_test" />
    <SUITE FILE_PATH="coverage/PycharmProject$QAM.coverage" NAME="QAM Coverage Results" MODIFIED="1744028368667" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Optical_communication_101" />
    <SUITE FILE_PATH="coverage/PycharmProject$8deg_EDWA_lens.coverage" NAME="8deg_EDWA_lens Coverage Results" MODIFIED="1748362562374" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/pump_to_EDWA/angled" />
    <SUITE FILE_PATH="coverage/PycharmProject$SiN_lens.coverage" NAME="SiN_lens Coverage Results" MODIFIED="1729786213875" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN/20deg" />
    <SUITE FILE_PATH="coverage/PycharmProject$dummy.coverage" NAME="dummy Coverage Results" MODIFIED="1741252021298" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/process_measurement/OSA" />
    <SUITE FILE_PATH="coverage/PycharmProject$QDMLLD_50GHz.coverage" NAME="QDMLLD_50GHz Coverage Results" MODIFIED="1730814209541" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$04_angled_7deg_EDWA_to_fiber__1_.coverage" NAME="04_angled_7deg_EDWA_to_fiber (1) Coverage Results" MODIFIED="1749989926835" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D174_01/opt_for_1480" />
    <SUITE FILE_PATH="coverage/PycharmProject$view_results.coverage" NAME="view_results Coverage Results" MODIFIED="1729677488999" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/utils" />
    <SUITE FILE_PATH="coverage/PycharmProject$obj.coverage" NAME="obj Coverage Results" MODIFIED="1734093742964" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/geometry/mesh_io" />
    <SUITE FILE_PATH="coverage/PycharmProject$PWB_test_D166.coverage" NAME="PWB_test_D166 Coverage Results" MODIFIED="1731052861729" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$m2_display.coverage" NAME="m2_display Coverage Results" MODIFIED="1727193895761" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$USER_HOME$/AppData/Local/anaconda3/envs/mfd-meas/Lib/site-packages/laserbeamsize" />
    <SUITE FILE_PATH="coverage/PycharmProject$02_straight_fiber_lens__1_.coverage" NAME="02_straight_fiber_lens (1) Coverage Results" MODIFIED="1753469158072" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550" />
    <SUITE FILE_PATH="coverage/PycharmProject$plot_trajectory.coverage" NAME="plot_trajectory Coverage Results" MODIFIED="1741803506842" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/PWB_NN/paper_plots" />
    <SUITE FILE_PATH="coverage/PycharmProject$EPFL_D213_doped_27cm.coverage" NAME="EPFL_D213_doped_27cm Coverage Results" MODIFIED="1752837561772" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$EPFL_D143_LGT.coverage" NAME="EPFL_D143_LGT Coverage Results" MODIFIED="1750334131270" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$plot_PN.coverage" NAME="plot_PN Coverage Results" MODIFIED="1753185178419" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/process_measurement/PNA" />
    <SUITE FILE_PATH="coverage/PycharmProject$5deg_pump_to_EDWA.coverage" NAME="5deg_pump_to_EDWA Coverage Results" MODIFIED="1731066254948" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/pump_to_EDWA/angled" />
    <SUITE FILE_PATH="coverage/PycharmProject$simple_script.coverage" NAME="simple_script Coverage Results" MODIFIED="1721237495261" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/obfuscation" />
    <SUITE FILE_PATH="coverage/PycharmProject$slicing.coverage" NAME="slicing Coverage Results" MODIFIED="1734359509178" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/geometry" />
    <SUITE FILE_PATH="coverage/PycharmProject$straight_pump_lens__1_.coverage" NAME="straight_pump_lens (1) Coverage Results" MODIFIED="1730974900872" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/straight" />
    <SUITE FILE_PATH="coverage/PycharmProject$field_distribution.coverage" NAME="field_distribution Coverage Results" MODIFIED="1753198820984" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple" />
    <SUITE FILE_PATH="coverage/PycharmProject$result_analyser.coverage" NAME="result_analyser Coverage Results" MODIFIED="1728303518164" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/lumpy/analyzation" />
    <SUITE FILE_PATH="coverage/PycharmProject$EPFL_D213_doped.coverage" NAME="EPFL_D213_doped Coverage Results" MODIFIED="1752770506128" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$IPQ_THSensors.coverage" NAME="IPQ-THSensors Coverage Results" MODIFIED="1755097393231" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/myutils" />
    <SUITE FILE_PATH="coverage/PycharmProject$01_straight_EDWA_lens_single_fit__1_.coverage" NAME="01_straight_EDWA_lens_single_fit (1) Coverage Results" MODIFIED="1753447282034" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_EDWA_lens.coverage" NAME="export_EDWA_lens Coverage Results" MODIFIED="1754467754332" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550_SQS" />
    <SUITE FILE_PATH="coverage/PycharmProject$test__1_.coverage" NAME="test (1) Coverage Results" MODIFIED="1724350867603" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Debug/vwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$coupling_axial_offset.coverage" NAME="coupling_axial_offset Coverage Results" MODIFIED="1733394524550" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/analysis" />
    <SUITE FILE_PATH="coverage/PycharmProject$different_solvers.coverage" NAME="different_solvers Coverage Results" MODIFIED="1727709733999" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Debug" />
    <SUITE FILE_PATH="coverage/PycharmProject$01_QD_MLLD_to_SiN_prism_v2.coverage" NAME="01_QD_MLLD_to_SiN_prism_v2 Coverage Results" MODIFIED="1748351246294" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/QD_MLLD_to_SiN" />
    <SUITE FILE_PATH="coverage/PycharmProject$cylinder_lens2.coverage" NAME="cylinder_lens2 Coverage Results" MODIFIED="1724759500716" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/OPALID_Cyl" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_pump_lens.coverage" NAME="export_pump_lens Coverage Results" MODIFIED="1731086103148" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/pump_to_EDWA/straight" />
    <SUITE FILE_PATH="coverage/PycharmProject$EPFL_SemiNex_pump.coverage" NAME="EPFL_SemiNex_pump Coverage Results" MODIFIED="1728914399573" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$prism_design.coverage" NAME="prism_design Coverage Results" MODIFIED="1730982926210" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/lens_design" />
    <SUITE FILE_PATH="coverage/PycharmProject$01_QD_MLLD_to_SiN__1_.coverage" NAME="01_QD_MLLD_to_SiN (1) Coverage Results" MODIFIED="1748352763443" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/QD_MLLD_to_SiN" />
    <SUITE FILE_PATH="coverage/PycharmProject$pml__1_.coverage" NAME="pml Coverage Results" MODIFIED="1722864999740" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wpm_mb" />
    <SUITE FILE_PATH="coverage/PycharmProject$sweep_b_d__1_.coverage" NAME="sweep_b_d Coverage Results" MODIFIED="1734105843519" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/OPALID_Cyl/Ellipsoid_lens" />
    <SUITE FILE_PATH="coverage/PycharmProject$03_7deg_EDWA_lens_single_fit.coverage" NAME="03_7deg_EDWA_lens_single_fit Coverage Results" MODIFIED="1754425883736" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550_SQS" />
    <SUITE FILE_PATH="coverage/PycharmProject$VLINK_16ch_pitch127um_lidless_fiberarray.coverage" NAME="VLINK_16ch_pitch127um_lidless_fiberarray Coverage Results" MODIFIED="1753467818860" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$double_CCS_low_noise.coverage" NAME="double_CCS_low_noise Coverage Results" MODIFIED="1738577052784" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/AeroDIODE" />
    <SUITE FILE_PATH="coverage/PycharmProject$cache.coverage" NAME="cache Coverage Results" MODIFIED="1721670929008" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Debug" />
    <SUITE FILE_PATH="coverage/PycharmProject$sim_helpers.coverage" NAME="sim_helpers Coverage Results" MODIFIED="1732975053183" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/utils" />
    <SUITE FILE_PATH="coverage/PycharmProject$straight_lens_gaussian_mfd_fit.coverage" NAME="straight_lens_gaussian_mfd_fit Coverage Results" MODIFIED="1748448871547" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D174_01" />
    <SUITE FILE_PATH="coverage/PycharmProject$gaussian_beam.coverage" NAME="gaussian_beam Coverage Results" MODIFIED="1724351099826" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Debug/vwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$QD_MLLD_to_SiN.coverage" NAME="QD_MLLD_to_SiN Coverage Results" MODIFIED="1728036572602" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN" />
    <SUITE FILE_PATH="coverage/PycharmProject$EPFL_D143_LGT_angled.coverage" NAME="EPFL_D143_LGT_angled Coverage Results" MODIFIED="1748024015025" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$example.coverage" NAME="example Coverage Results" MODIFIED="1738575698882" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/AeroDIODE" />
    <SUITE FILE_PATH="coverage/PycharmProject$build_laser_lens.coverage" NAME="build_laser_lens Coverage Results" MODIFIED="1729783793447" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/Inventor" />
    <SUITE FILE_PATH="coverage/PycharmProject$split_model.coverage" NAME="split_model Coverage Results" MODIFIED="1729087645421" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/Inventor" />
    <SUITE FILE_PATH="coverage/PycharmProject$straight_EDWA_lens_default_mfd_fit.coverage" NAME="straight_EDWA_lens_default_mfd_fit Coverage Results" MODIFIED="1731064282317" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/EDWA_to_fiber" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_model_SiN_lens.coverage" NAME="export_model_SiN_lens Coverage Results" MODIFIED="1748352153940" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/QD_MLLD_to_SiN/CLEO_pair" />
    <SUITE FILE_PATH="coverage/PycharmProject$05_8deg_EDWA_to_fiber_used__1_.coverage" NAME="05_8deg_EDWA_to_fiber_used (1) Coverage Results" MODIFIED="1753728538923" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1480" />
    <SUITE FILE_PATH="coverage/PycharmProject$cylinder_lens_angled.coverage" NAME="cylinder_lens_angled Coverage Results" MODIFIED="1726042063643" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/OPALID_Cyl" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_model_SiN_lens_d3.coverage" NAME="export_model_SiN_lens_d3 Coverage Results" MODIFIED="1730151814215" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN/20deg" />
    <SUITE FILE_PATH="coverage/PycharmProject$mfd50_8_deg.coverage" NAME="mfd50_8_deg Coverage Results" MODIFIED="1754032536137" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/Rotating_lidar_David" />
    <SUITE FILE_PATH="coverage/PycharmProject$EDWA_lens.coverage" NAME="EDWA_lens Coverage Results" MODIFIED="1730916115653" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/straight" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_model_QDMLLD_lens_v2.coverage" NAME="export_model_QDMLLD_lens_v2 Coverage Results" MODIFIED="1729184386633" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN/debug" />
    <SUITE FILE_PATH="coverage/PycharmProject$01_straight_EDWA_lens_d4sigma_fit__1_.coverage" NAME="01_straight_EDWA_lens_d4sigma_fit (1) Coverage Results" MODIFIED="1751802545931" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D143_LGT/opt_for_1480" />
    <SUITE FILE_PATH="coverage/PycharmProject$image_into_gif.coverage" NAME="image_into_gif Coverage Results" MODIFIED="1752841484680" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/myutils" />
    <SUITE FILE_PATH="coverage/PycharmProject$pump_die_to_EDWA.coverage" NAME="pump_die_to_EDWA Coverage Results" MODIFIED="1730986585041" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/angled" />
    <SUITE FILE_PATH="coverage/PycharmProject$LIV_measurement.coverage" NAME="LIV_measurement Coverage Results" MODIFIED="1739205145863" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/LIV_measuremet" />
    <SUITE FILE_PATH="coverage/PycharmProject$pump_lens__1_.coverage" NAME="pump_lens (1) Coverage Results" MODIFIED="1730922800273" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/straight" />
    <SUITE FILE_PATH="coverage/PycharmProject$send_mails.coverage" NAME="send_mails Coverage Results" MODIFIED="1740665865365" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/myutils" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_model_fiber.coverage" NAME="export_model_fiber Coverage Results" MODIFIED="1754914818176" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D143_LGT/C9_F6_opt_for_1550_gaussian" />
    <SUITE FILE_PATH="coverage/PycharmProject$10deg_EDWA_lens.coverage" NAME="10deg_EDWA_lens Coverage Results" MODIFIED="1730987151654" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/angled" />
    <SUITE FILE_PATH="coverage/PycharmProject$sweep_b_d_old.coverage" NAME="sweep_b_d_old Coverage Results" MODIFIED="1733997735732" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/OPALID_Cyl/Ellipsoid_lens" />
    <SUITE FILE_PATH="coverage/PycharmProject$01_QD_MLLD_to_SiN_prism_v2_offset_tolerance.coverage" NAME="01_QD_MLLD_to_SiN_prism_v2_offset_tolerance Coverage Results" MODIFIED="1732828252574" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN" />
    <SUITE FILE_PATH="coverage/PycharmProject$x_straight_pump_lens__1_.coverage" NAME="x_straight_pump_lens (1) Coverage Results" MODIFIED="1730974760477" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/straight/intermediate_steps" />
    <SUITE FILE_PATH="coverage/PycharmProject$05_7deg_EDWA_to_fiber_used__1_.coverage" NAME="05_7deg_EDWA_to_fiber_used (1) Coverage Results" MODIFIED="1754467534442" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550_SQS" />
    <SUITE FILE_PATH="coverage/PycharmProject$plot_QDMLLD_spectrum.coverage" NAME="plot_QDMLLD_spectrum Coverage Results" MODIFIED="1753181437476" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/process_measurement/OSA" />
    <SUITE FILE_PATH="coverage/PycharmProject$optics.coverage" NAME="optics Coverage Results" MODIFIED="1730188724985" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/utils" />
    <SUITE FILE_PATH="coverage/PycharmProject$straight_pump_lens.coverage" NAME="straight_pump_lens Coverage Results" MODIFIED="1731845310878" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/pump_to_EDWA/straight" />
    <SUITE FILE_PATH="coverage/PycharmProject$QDMLLD_to_SiN_d3.coverage" NAME="QDMLLD_to_SiN_d3 Coverage Results" MODIFIED="1730908622090" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN/20deg" />
    <SUITE FILE_PATH="coverage/PycharmProject$QDMLLD_49p7_J5.coverage" NAME="QDMLLD_49p7_J5 Coverage Results" MODIFIED="1728556329670" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement" />
    <SUITE FILE_PATH="coverage/PycharmProject$plots.coverage" NAME="plots Coverage Results" MODIFIED="1742316246354" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/EDWA_gain" />
    <SUITE FILE_PATH="coverage/PycharmProject$plot_sweep.coverage" NAME="plot_sweep Coverage Results" MODIFIED="1733999618019" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/OPALID_Cyl/Ellipsoid_lens" />
    <SUITE FILE_PATH="coverage/PycharmProject$polynomial_surf.coverage" NAME="polynomial_surf Coverage Results" MODIFIED="1721681191230" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Debug" />
    <SUITE FILE_PATH="coverage/PycharmProject$tilted_slab_test.coverage" NAME="tilted_slab_test Coverage Results" MODIFIED="1724765174450" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wave_propagation_method/designs/tilted_slab_test" />
    <SUITE FILE_PATH="coverage/PycharmProject$y_straight_pump_lens.coverage" NAME="y_straight_pump_lens Coverage Results" MODIFIED="1730976856231" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/straight/intermediate_steps" />
    <SUITE FILE_PATH="coverage/PycharmProject$check_field.coverage" NAME="check_field Coverage Results" MODIFIED="1747416667700" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/OPALID_Cyl" />
    <SUITE FILE_PATH="coverage/PycharmProject$PML_reflection_investigate.coverage" NAME="PML_reflection_investigate Coverage Results" MODIFIED="1728302664221" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/lumpy/scripts" />
    <SUITE FILE_PATH="coverage/PycharmProject$goldeye_G034_TEC1.coverage" NAME="goldeye_G034_TEC1 Coverage Results" MODIFIED="1727367328792" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Goldeye" />
    <SUITE FILE_PATH="coverage/PycharmProject$debug.coverage" NAME="debug Coverage Results" MODIFIED="1742458921361" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/../Temp/wpm_lens_design/designs/Hybrid_combs_packaging/low_backreflection/debug" />
    <SUITE FILE_PATH="coverage/PycharmProject$camera.coverage" NAME="camera Coverage Results" MODIFIED="1726500713022" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/mfd_measurement" />
    <SUITE FILE_PATH="coverage/PycharmProject$propagate_imported_field.coverage" NAME="propagate_imported_field Coverage Results" MODIFIED="1753199310559" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/OPALID_Cyl" />
    <SUITE FILE_PATH="coverage/PycharmProject$pytest_in_test_pml_py.coverage" NAME="pytest in test_pml.py Coverage Results" MODIFIED="1722863987667" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wpm_mb" />
    <SUITE FILE_PATH="coverage/PycharmProject$05_7deg_EDWA_to_fiber__1_.coverage" NAME="05_7deg_EDWA_to_fiber (1) Coverage Results" MODIFIED="1753636339547" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550" />
    <SUITE FILE_PATH="coverage/PycharmProject$prism.coverage" NAME="prism Coverage Results" MODIFIED="1724334997202" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Debug/vwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$loadfilter__1_.coverage" NAME="loadfilter (1) Coverage Results" MODIFIED="1750426367260" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/II_VI_waveshaper" />
    <SUITE FILE_PATH="coverage/PycharmProject$evan.coverage" NAME="evan Coverage Results" MODIFIED="1724695463777" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Debug/vwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$obj2ipt.coverage" NAME="obj2ipt Coverage Results" MODIFIED="1748616612181" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/Inventor" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_EDWA_lens_straight.coverage" NAME="export_EDWA_lens_straight Coverage Results" MODIFIED="1755091947370" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F3_doped_26cm_opt_for_1550_SQS" />
    <SUITE FILE_PATH="coverage/PycharmProject$bwpms__1_.coverage" NAME="bwpms (1) Coverage Results" MODIFIED="1726146748314" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$plot_RF_phase_noise.coverage" NAME="plot_RF_phase_noise Coverage Results" MODIFIED="1753177090914" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/process_measurement/PNA" />
    <SUITE FILE_PATH="coverage/PycharmProject$DTU_DFB.coverage" NAME="DTU_DFB Coverage Results" MODIFIED="1739202208708" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$coupling_translation_offset.coverage" NAME="coupling_translation_offset Coverage Results" MODIFIED="1748611147948" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/analysis" />
    <SUITE FILE_PATH="coverage/PycharmProject$01_straight_EDWA_lens_d4sigma_fit.coverage" NAME="01_straight_EDWA_lens_d4sigma_fit Coverage Results" MODIFIED="1752858131282" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/doped_26cm_opt_for_1480" />
    <SUITE FILE_PATH="coverage/PycharmProject$pytest_in_test_pyfocus_py.coverage" NAME="pytest in test_pyfocus.py Coverage Results" MODIFIED="1724347158571" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/PyFocus/src" />
    <SUITE FILE_PATH="coverage/PycharmProject$beam_direction_debug.coverage" NAME="beam_direction_debug Coverage Results" MODIFIED="1726043817510" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Debug" />
    <SUITE FILE_PATH="coverage/PycharmProject$ai_test.coverage" NAME="ai_test Coverage Results" MODIFIED="1740140042859" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/NIDAQmx_driver" />
    <SUITE FILE_PATH="coverage/PycharmProject$image_to_video.coverage" NAME="image_to_video Coverage Results" MODIFIED="1752843421205" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/myutils" />
    <SUITE FILE_PATH="coverage/PycharmProject$32_straight_fiber_lens_vlink_25MFD.coverage" NAME="32_straight_fiber_lens_vlink_25MFD Coverage Results" MODIFIED="1754920416721" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D143_LGT/C9_F6_opt_for_1550_gaussian" />
    <SUITE FILE_PATH="coverage/PycharmProject$AMF_lens_50_air_gap.coverage" NAME="AMF_lens_50_air_gap Coverage Results" MODIFIED="1730992993366" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wave_propagation_method/designs/AMF_lens_Patrick" />
    <SUITE FILE_PATH="coverage/PycharmProject$LTB1_VOA_wrapper.coverage" NAME="LTB1_VOA_wrapper Coverage Results" MODIFIED="1742207078370" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/EXPO_LTB1_VOA" />
    <SUITE FILE_PATH="coverage/PycharmProject$LIV_PI_curve.coverage" NAME="LIV_PI_curve Coverage Results" MODIFIED="1741354946641" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/laser_characterization" />
    <SUITE FILE_PATH="coverage/PycharmProject$Figure_S13_chip_facet_reflectivity.coverage" NAME="Figure_S13_chip_facet_reflectivity Coverage Results" MODIFIED="1753349368818" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/EDWA_characterization" />
    <SUITE FILE_PATH="coverage/PycharmProject$repaire_mesh.coverage" NAME="repaire_mesh Coverage Results" MODIFIED="1729092581925" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/Inventor" />
    <SUITE FILE_PATH="coverage/PycharmProject$EPFL_D211.coverage" NAME="EPFL_D211 Coverage Results" MODIFIED="1750338350127" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$05_8deg_EDWA_to_fiber_used__2_.coverage" NAME="05_8deg_EDWA_to_fiber_used (2) Coverage Results" MODIFIED="1753645891708" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550/temp" />
    <SUITE FILE_PATH="coverage/PycharmProject$QD_MLLD_to_SiN_x_offset.coverage" NAME="QD_MLLD_to_SiN_x_offset Coverage Results" MODIFIED="1728383866161" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN" />
    <SUITE FILE_PATH="coverage/PycharmProject$model_importer.coverage" NAME="model_importer Coverage Results" MODIFIED="1734368555118" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$smc100__1_.coverage" NAME="smc100 (1) Coverage Results" MODIFIED="1727369876255" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/SMC100" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_model_QDMLLD_lens_d3.coverage" NAME="export_model_QDMLLD_lens_d3 Coverage Results" MODIFIED="1729337810046" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN/20deg" />
    <SUITE FILE_PATH="coverage/PycharmProject$simple_lens_with_imported_field.coverage" NAME="simple_lens_with_imported_field Coverage Results" MODIFIED="1747312724664" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/OPALID_Cyl" />
    <SUITE FILE_PATH="coverage/PycharmProject$THz_independent.coverage" NAME="THz_independent Coverage Results" MODIFIED="1734706051840" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/OPALID_Cyl" />
    <SUITE FILE_PATH="coverage/PycharmProject$obr_data_processing.coverage" NAME="obr_data_processing Coverage Results" MODIFIED="1732657192917" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/process_measurement/OBR_processing" />
    <SUITE FILE_PATH="coverage/PycharmProject$5deg_pump_to_EDWA__1_.coverage" NAME="5deg_pump_to_EDWA (1) Coverage Results" MODIFIED="1731053383490" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/angled" />
    <SUITE FILE_PATH="coverage/PycharmProject$wsp_generator__1_.coverage" NAME="wsp_generator (1) Coverage Results" MODIFIED="1751308940770" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/II_VI_waveshaper" />
    <SUITE FILE_PATH="coverage/PycharmProject$dev.coverage" NAME="dev Coverage Results" MODIFIED="1750259689611" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/II-VI_waveshaper" />
    <SUITE FILE_PATH="coverage/PycharmProject$test_pyfocus.coverage" NAME="test_pyfocus Coverage Results" MODIFIED="1724346762226" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="" />
    <SUITE FILE_PATH="coverage/PycharmProject$import_data.coverage" NAME="import_data Coverage Results" MODIFIED="1731064678319" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/THz_lens" />
    <SUITE FILE_PATH="coverage/PycharmProject$build_straight_lens.coverage" NAME="build_straight_lens Coverage Results" MODIFIED="1748616682629" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/Inventor" />
    <SUITE FILE_PATH="coverage/PycharmProject$cal_delayline_mismatch.coverage" NAME="cal_delayline_mismatch Coverage Results" MODIFIED="1733482362701" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/process_measurement/OBR_processing" />
    <SUITE FILE_PATH="coverage/PycharmProject$imported_field_and_whole_lens.coverage" NAME="imported_field_and_whole_lens Coverage Results" MODIFIED="1749727277863" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/OPALID_Cyl" />
    <SUITE FILE_PATH="coverage/PycharmProject$SemiNex_pump_186Test_2_with_lens.coverage" NAME="SemiNex_pump_186Test-2_with_lens Coverage Results" MODIFIED="1731846662801" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$imported_field_and_whole_lens__1_.coverage" NAME="imported_field_and_whole_lens (1) Coverage Results" MODIFIED="1747988489273" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/OPALID_Cyl" />
    <SUITE FILE_PATH="coverage/PycharmProject$y_straight_EDWA_lens.coverage" NAME="y_straight_EDWA_lens Coverage Results" MODIFIED="1730976671055" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/straight/intermediate_steps" />
    <SUITE FILE_PATH="coverage/PycharmProject$01_QD_MLLD_to_SiN_prism_v2_CLEO_plot.coverage" NAME="01_QD_MLLD_to_SiN_prism_v2_CLEO_plot Coverage Results" MODIFIED="1733917445943" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN" />
    <SUITE FILE_PATH="coverage/PycharmProject$SemiNex_pump_186_2.coverage" NAME="SemiNex_pump_186-2 Coverage Results" MODIFIED="1731847238328" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$plot_EDWA_spectrum.coverage" NAME="plot_EDWA_spectrum Coverage Results" MODIFIED="1741633714714" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/process_measurement/OSA" />
    <SUITE FILE_PATH="coverage/PycharmProject$read_data.coverage" NAME="read_data Coverage Results" MODIFIED="1749202112234" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/OPALID_Cyl/sweep_ellipsoid_lens" />
    <SUITE FILE_PATH="coverage/PycharmProject$03_8deg_EDWA_lens_single_fit.coverage" NAME="03_8deg_EDWA_lens_single_fit Coverage Results" MODIFIED="1753468834750" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1480" />
    <SUITE FILE_PATH="coverage/PycharmProject$plot_IQ_vector.coverage" NAME="plot_IQ_vector Coverage Results" MODIFIED="1732657690704" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/process_measurement/PNA" />
    <SUITE FILE_PATH="coverage/PycharmProject$FP_resonance__1_.coverage" NAME="FP_resonance (1) Coverage Results" MODIFIED="1753435964921" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/EDWA_characterization/Facet_reflection" />
    <SUITE FILE_PATH="coverage/PycharmProject$THz_utils.coverage" NAME="THz_utils Coverage Results" MODIFIED="1730297757136" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/functionalities" />
    <SUITE FILE_PATH="coverage/PycharmProject$MFD_30.coverage" NAME="MFD_30 Coverage Results" MODIFIED="1754918656536" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/SMF" />
    <SUITE FILE_PATH="coverage/PycharmProject$imported_cst_field_test.coverage" NAME="imported_cst_field_test Coverage Results" MODIFIED="1739807287753" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/OPALID_Cyl/Ellipsoid_lens" />
    <SUITE FILE_PATH="coverage/PycharmProject$21_straight_fiber_lens_SQS_30MFD.coverage" NAME="21_straight_fiber_lens_SQS_30MFD Coverage Results" MODIFIED="1755006022822" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D143_LGT/C9_F6_opt_for_1550_gaussian" />
    <SUITE FILE_PATH="coverage/PycharmProject$04_8deg_fiber_lens__1_.coverage" NAME="04_8deg_fiber_lens (1) Coverage Results" MODIFIED="1753470003597" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550" />
    <SUITE FILE_PATH="coverage/PycharmProject$plot_spectrum.coverage" NAME="plot_spectrum Coverage Results" MODIFIED="1740659625387" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/process_measurement/OSA/OSA_spectrum" />
    <SUITE FILE_PATH="coverage/PycharmProject$prism_angles_opt.coverage" NAME="prism_angles_opt Coverage Results" MODIFIED="1730982882425" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/functionalities" />
    <SUITE FILE_PATH="coverage/PycharmProject$05_8deg_EDWA_to_fiber.coverage" NAME="05_8deg_EDWA_to_fiber Coverage Results" MODIFIED="1754411844642" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550_SQS" />
    <SUITE FILE_PATH="coverage/PycharmProject$InP_refr_tilted_pretest.coverage" NAME="InP_refr_tilted_pretest Coverage Results" MODIFIED="1742377539178" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/../Temp/wpm_lens_design/designs/InP_refractive_tilted_pretrest" />
    <SUITE FILE_PATH="coverage/PycharmProject$angled_7deg_EDWA_to_fiber.coverage" NAME="angled_7deg_EDWA_to_fiber Coverage Results" MODIFIED="1748631130717" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D174_01" />
    <SUITE FILE_PATH="coverage/PycharmProject$unit_converter.coverage" NAME="unit_converter Coverage Results" MODIFIED="1751197727538" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/myutils" />
    <SUITE FILE_PATH="coverage/PycharmProject$straight_EDWA_lens__1_.coverage" NAME="straight_EDWA_lens (1) Coverage Results" MODIFIED="1730968244144" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/straight" />
    <SUITE FILE_PATH="coverage/PycharmProject$sweep_b_d.coverage" NAME="sweep_b_d Coverage Results" MODIFIED="1743168764358" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/OPALID_Cyl/Ellipsoid_lens" />
    <SUITE FILE_PATH="coverage/PycharmProject$with_GUI.coverage" NAME="with_GUI Coverage Results" MODIFIED="1738765278027" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Thorlab_PMxxx" />
    <SUITE FILE_PATH="coverage/PycharmProject$AMF22_EC.coverage" NAME="AMF22_EC Coverage Results" MODIFIED="1727195471420" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/MFD-fit/laserbeamsize_fit" />
    <SUITE FILE_PATH="coverage/PycharmProject$passive_assembly_plots.coverage" NAME="passive_assembly_plots Coverage Results" MODIFIED="1742378834960" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/../Temp/wpm_lens_design/tools" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_model_gaussian_fit.coverage" NAME="export_model_gaussian_fit Coverage Results" MODIFIED="1731072904071" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/EDWA_to_fiber" />
    <SUITE FILE_PATH="coverage/PycharmProject$SMF_to_AMF_EC__1_.coverage" NAME="SMF_to_AMF_EC (1) Coverage Results" MODIFIED="1729106966226" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/SMF_to_AMF" />
    <SUITE FILE_PATH="coverage/PycharmProject$plot_PI_manual.coverage" NAME="plot_PI_manual Coverage Results" MODIFIED="1727188158121" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/LIV_measuremet" />
    <SUITE FILE_PATH="coverage/PycharmProject$QDMLLD_to_SiN_d1.coverage" NAME="QDMLLD_to_SiN_d1 Coverage Results" MODIFIED="1729334969319" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN/10deg" />
    <SUITE FILE_PATH="coverage/PycharmProject$temp.coverage" NAME="temp Coverage Results" MODIFIED="1753364179752" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/EDWA_characterization" />
    <SUITE FILE_PATH="coverage/PycharmProject$propagate_measured_mf.coverage" NAME="propagate_measured_mf Coverage Results" MODIFIED="1753441005470" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/propagate_measurement" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_model_QDMLLD_lens.coverage" NAME="export_model_QDMLLD_lens Coverage Results" MODIFIED="1729184692371" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN/10deg" />
    <SUITE FILE_PATH="coverage/PycharmProject$wpms__3_.coverage" NAME="wpms (3) Coverage Results" MODIFIED="1726237524685" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$03_8deg_EDWA_lens_d4sigma_fit.coverage" NAME="03_8deg_EDWA_lens_d4sigma_fit Coverage Results" MODIFIED="1752908224487" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1480" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_EDWA_lens__1_.coverage" NAME="export_EDWA_lens (1) Coverage Results" MODIFIED="1753778850881" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550" />
    <SUITE FILE_PATH="coverage/PycharmProject$reversed_path_optimized.coverage" NAME="reversed_path_optimized Coverage Results" MODIFIED="1748351812611" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wave_propagation_method/designs/Hybrid_combs_packaging/low_backreflection" />
    <SUITE FILE_PATH="coverage/PycharmProject$SemiNex_pump_1.coverage" NAME="SemiNex_pump_1 Coverage Results" MODIFIED="1730912135587" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$05_7deg_EDWA_to_fiber__2_.coverage" NAME="05_7deg_EDWA_to_fiber (2) Coverage Results" MODIFIED="1753645423770" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550/temp" />
    <SUITE FILE_PATH="coverage/PycharmProject$image_histogram.coverage" NAME="image_histogram Coverage Results" MODIFIED="1727372200889" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement" />
    <SUITE FILE_PATH="coverage/PycharmProject$B2902A_wrapper_temp.coverage" NAME="B2902A_wrapper_temp Coverage Results" MODIFIED="1740757729042" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Keysight_B2902A_driver" />
    <SUITE FILE_PATH="coverage/PycharmProject$03_7deg_EDWA_lens_d4sigma_fit__2_.coverage" NAME="03_7deg_EDWA_lens_d4sigma_fit (2) Coverage Results" MODIFIED="1751806918609" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D143_LGT/opt_for_1480_8deg" />
    <SUITE FILE_PATH="coverage/PycharmProject$QDMLLD_to_SiN_d2.coverage" NAME="QDMLLD_to_SiN_d2 Coverage Results" MODIFIED="1729334960146" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN/10deg" />
    <SUITE FILE_PATH="coverage/PycharmProject$single_CCS_low_noise__1_.coverage" NAME="single_CCS_low_noise (1) Coverage Results" MODIFIED="1738577000451" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/AeroDIODE" />
    <SUITE FILE_PATH="coverage/PycharmProject$SMF_to_AMF_EC.coverage" NAME="SMF_to_AMF_EC Coverage Results" MODIFIED="1754926345034" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/SMF_to_AMF" />
    <SUITE FILE_PATH="coverage/PycharmProject$QD_MLLD_only_tilt.coverage" NAME="QD_MLLD_only_tilt Coverage Results" MODIFIED="1742458992867" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/../Temp/wpm_lens_design/designs/Hybrid_combs_packaging/low_backreflection/debug" />
    <SUITE FILE_PATH="coverage/PycharmProject$control_TLB6700__1_.coverage" NAME="control_TLB6700 (1) Coverage Results" MODIFIED="1742218451958" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Newfocus_TLB6700" />
    <SUITE FILE_PATH="coverage/PycharmProject$GUI_2ch.coverage" NAME="GUI_2ch Coverage Results" MODIFIED="1738767381496" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Thorlab_PMxxx" />
    <SUITE FILE_PATH="coverage/PycharmProject$visualize_mesh.coverage" NAME="visualize_mesh Coverage Results" MODIFIED="1741714029427" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/PWB_NN/visualization" />
    <SUITE FILE_PATH="coverage/PycharmProject$main.coverage" NAME="main Coverage Results" MODIFIED="1739206543990" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/LIV_measuremet" />
    <SUITE FILE_PATH="coverage/PycharmProject$beam_angle_debug.coverage" NAME="beam_angle_debug Coverage Results" MODIFIED="1742458171379" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/../Temp/wpm_lens_design/designs/Hybrid_combs_packaging" />
    <SUITE FILE_PATH="coverage/PycharmProject$810nm_LF.coverage" NAME="810nm_LF Coverage Results" MODIFIED="1742457980543" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/../Temp/wpm_lens_design/designs/design study Meri" />
    <SUITE FILE_PATH="coverage/PycharmProject$brewster_angle.coverage" NAME="brewster_angle Coverage Results" MODIFIED="1725018406131" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Debug/vwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$PWB_test_D141A_F5_C3.coverage" NAME="PWB_test_D141A_F5_C3 Coverage Results" MODIFIED="1730915986778" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$MFD40_1surf__1_.coverage" NAME="MFD40_1surf (1) Coverage Results" MODIFIED="1731061770472" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/EDWA_to_fiber" />
    <SUITE FILE_PATH="coverage/PycharmProject$boundary_conditions.coverage" NAME="boundary_conditions Coverage Results" MODIFIED="1723220242478" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$NewFocus_TLB6700_wrapper.coverage" NAME="NewFocus_TLB6700_wrapper Coverage Results" MODIFIED="1742218153222" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Newfocus_TLB-6700" />
    <SUITE FILE_PATH="coverage/PycharmProject$mfd_calculation_debug.coverage" NAME="mfd_calculation_debug Coverage Results" MODIFIED="1725977807746" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Debug" />
    <SUITE FILE_PATH="coverage/PycharmProject$01_QD_MLLD_to_SiN.coverage" NAME="01_QD_MLLD_to_SiN Coverage Results" MODIFIED="1750583473314" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/QD_MLLD_to_SiN/CLEO_pair" />
    <SUITE FILE_PATH="coverage/PycharmProject$meas.coverage" NAME="meas Coverage Results" MODIFIED="1727288408347" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement" />
    <SUITE FILE_PATH="coverage/PycharmProject$cut_lens__1_.coverage" NAME="cut_lens (1) Coverage Results" MODIFIED="1729087738338" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/Inventor" />
    <SUITE FILE_PATH="coverage/PycharmProject$GUI_multi_ch_plot.coverage" NAME="GUI_multi_ch_plot Coverage Results" MODIFIED="1751901631296" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Thorlabs_PMxxx" />
    <SUITE FILE_PATH="coverage/PycharmProject$control_TLB6700.coverage" NAME="control_TLB6700 Coverage Results" MODIFIED="1742217538828" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Newfocus_TLB-6700" />
    <SUITE FILE_PATH="coverage/PycharmProject$structures.coverage" NAME="structures Coverage Results" MODIFIED="1738932234965" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple" />
    <SUITE FILE_PATH="coverage/PycharmProject$merge_pdf.coverage" NAME="merge_pdf Coverage Results" MODIFIED="1733262770861" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Tools" />
    <SUITE FILE_PATH="coverage/PycharmProject$reflectivity_characterization.coverage" NAME="reflectivity_characterization Coverage Results" MODIFIED="1753279298255" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/EDWA_characterization" />
    <SUITE FILE_PATH="coverage/PycharmProject$d_BF.coverage" NAME="d-BF Coverage Results" MODIFIED="1734533592019" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Teaching/ps04_student" />
    <SUITE FILE_PATH="coverage/PycharmProject$mesh.coverage" NAME="mesh Coverage Results" MODIFIED="1734020743143" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/geometry" />
    <SUITE FILE_PATH="coverage/PycharmProject$12_straight_EDWA_lens_gaussian_fit_25MFD.coverage" NAME="12_straight_EDWA_lens_gaussian_fit_25MFD Coverage Results" MODIFIED="1754919828192" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D143_LGT/C9_F6_opt_for_1550_gaussian" />
    <SUITE FILE_PATH="coverage/PycharmProject$AMF_MFD50.coverage" NAME="AMF_MFD50 Coverage Results" MODIFIED="1730227656442" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Patrick" />
    <SUITE FILE_PATH="coverage/PycharmProject$Th14.coverage" NAME="Th14 Coverage Results" MODIFIED="1755086598634" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/myutils" />
    <SUITE FILE_PATH="coverage/PycharmProject$far_field.coverage" NAME="far_field Coverage Results" MODIFIED="1747736455512" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/functionalities" />
    <SUITE FILE_PATH="coverage/PycharmProject$build_straight_lens_side_print.coverage" NAME="build_straight_lens_side_print Coverage Results" MODIFIED="1755092531518" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/Inventor" />
    <SUITE FILE_PATH="coverage/PycharmProject$x_straight_EDWA_lens.coverage" NAME="x_straight_EDWA_lens Coverage Results" MODIFIED="1730976669030" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/straight/intermediate_steps" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_fiber_lens__1_.coverage" NAME="export_fiber_lens (1) Coverage Results" MODIFIED="1753781510066" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550" />
    <SUITE FILE_PATH="coverage/PycharmProject$05_7deg_1480.coverage" NAME="05_7deg_1480 Coverage Results" MODIFIED="1753621052159" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_model_QDMLLD_lens_d4.coverage" NAME="export_model_QDMLLD_lens_d4 Coverage Results" MODIFIED="1729779674836" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN/20deg" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_model_QD_lens.coverage" NAME="export_model_QD_lens Coverage Results" MODIFIED="1742458216028" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/../Temp/wpm_lens_design/designs/Hybrid_combs_packaging/CLEO" />
    <SUITE FILE_PATH="coverage/PycharmProject$TIR.coverage" NAME="TIR Coverage Results" MODIFIED="1724409117462" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Debug/vwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$wpms.coverage" NAME="wpms Coverage Results" MODIFIED="1725966958885" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$QD_MLLD_lens_debug.coverage" NAME="QD_MLLD_lens_debug Coverage Results" MODIFIED="1729340467466" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN/20deg" />
    <SUITE FILE_PATH="coverage/PycharmProject$05_8deg_EDWA_to_fiber__1_.coverage" NAME="05_8deg_EDWA_to_fiber (1) Coverage Results" MODIFIED="1753647112246" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1480" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_model_EDWA_11.coverage" NAME="export_model_EDWA_11 Coverage Results" MODIFIED="1754922416678" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D143_LGT/C9_F6_opt_for_1550_gaussian" />
    <SUITE FILE_PATH="coverage/PycharmProject$11_straight_EDWA_lens_gaussian_fit_30MFD.coverage" NAME="11_straight_EDWA_lens_gaussian_fit_30MFD Coverage Results" MODIFIED="1754919801880" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D143_LGT/C9_F6_opt_for_1550_gaussian" />
    <SUITE FILE_PATH="coverage/PycharmProject$8deg_pump_to_EDWA.coverage" NAME="8deg_pump_to_EDWA Coverage Results" MODIFIED="1748362446948" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/pump_to_EDWA/angled" />
    <SUITE FILE_PATH="coverage/PycharmProject$EPFL_D211_F1_F3.coverage" NAME="EPFL_D211_F1_F3 Coverage Results" MODIFIED="1752768427903" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$PMxxx_SCPI_pyvisa__1_.coverage" NAME="PMxxx_SCPI_pyvisa (1) Coverage Results" MODIFIED="1738685300076" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Thorlab_PMxxx" />
    <SUITE FILE_PATH="coverage/PycharmProject$noise_figure.coverage" NAME="noise_figure Coverage Results" MODIFIED="1727170726649" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/EDWA" />
    <SUITE FILE_PATH="coverage/PycharmProject$Lorentzian_linewidth.coverage" NAME="Lorentzian_linewidth Coverage Results" MODIFIED="1748249930730" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/process_measurement/utils" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_model_QDMLLD_lens_d2.coverage" NAME="export_model_QDMLLD_lens_d2 Coverage Results" MODIFIED="1729171006411" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN/10deg" />
    <SUITE FILE_PATH="coverage/PycharmProject$crop_img.coverage" NAME="crop_img Coverage Results" MODIFIED="1748255826006" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/myutils" />
    <SUITE FILE_PATH="coverage/PycharmProject$OSA_save_spectrum.coverage" NAME="OSA_save_spectrum Coverage Results" MODIFIED="1742217495632" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/AQ6370D_OSA" />
    <SUITE FILE_PATH="coverage/PycharmProject$sweep_b_d_imported_field.coverage" NAME="sweep_b_d_imported_field Coverage Results" MODIFIED="1749200611367" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/OPALID_Cyl/sweep_ellipsoid_lens" />
    <SUITE FILE_PATH="coverage/PycharmProject$QD_MLLD_lens_F2.coverage" NAME="QD_MLLD_lens_F2 Coverage Results" MODIFIED="1742374433443" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wave_propagation_method/designs/Hybrid_combs_packaging/in_plane/first_design_1550" />
    <SUITE FILE_PATH="coverage/PycharmProject$04_8deg_fiber_lens.coverage" NAME="04_8deg_fiber_lens Coverage Results" MODIFIED="1754338274818" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550_SQS" />
    <SUITE FILE_PATH="coverage/PycharmProject$imported_lens.coverage" NAME="imported_lens Coverage Results" MODIFIED="1741361150067" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/OPALID_Cyl" />
    <SUITE FILE_PATH="coverage/PycharmProject$wpms_TE.coverage" NAME="wpms_TE Coverage Results" MODIFIED="1723666707587" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$05_8deg_EDWA_to_fiber_used.coverage" NAME="05_8deg_EDWA_to_fiber_used Coverage Results" MODIFIED="1754319912348" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1480" />
    <SUITE FILE_PATH="coverage/PycharmProject$DTU_.coverage" NAME="DTU_ Coverage Results" MODIFIED="1737980960164" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$DTU_SiC__1_.coverage" NAME="DTU_SiC (1) Coverage Results" MODIFIED="1738071340786" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$SMF_expander_50.coverage" NAME="SMF_expander_50 Coverage Results" MODIFIED="1724227574780" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wave_propagation_method/designs/Rotating_lidar_David" />
    <SUITE FILE_PATH="coverage/PycharmProject$plot_data.coverage" NAME="plot_data Coverage Results" MODIFIED="1727184664543" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/LIV_measuremet" />
    <SUITE FILE_PATH="coverage/PycharmProject$gpt_r_extraction.coverage" NAME="gpt_r_extraction Coverage Results" MODIFIED="1753351097052" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/EDWA_characterization" />
    <SUITE FILE_PATH="coverage/PycharmProject$sweep_finer.coverage" NAME="sweep_finer Coverage Results" MODIFIED="1734470823138" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/OPALID_Cyl/Ellipsoid_lens" />
    <SUITE FILE_PATH="coverage/PycharmProject$05_7deg_EDWA_to_fiber_used.coverage" NAME="05_7deg_EDWA_to_fiber_used Coverage Results" MODIFIED="1754421117375" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550_SQS/wo_opt_lens2_pos" />
    <SUITE FILE_PATH="coverage/PycharmProject$array_io.coverage" NAME="array_io Coverage Results" MODIFIED="1727713378135" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/Debug/vwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$wavelength_power_sweep.coverage" NAME="wavelength_power_sweep Coverage Results" MODIFIED="1742307848564" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/EDWA_gain" />
    <SUITE FILE_PATH="coverage/PycharmProject$straight_pump_lens_temp.coverage" NAME="straight_pump_lens_temp Coverage Results" MODIFIED="1730972931278" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/straight" />
    <SUITE FILE_PATH="coverage/PycharmProject$EPFL_350nm_test_wg.coverage" NAME="EPFL_350nm_test_wg Coverage Results" MODIFIED="1752857153377" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$Test.coverage" NAME="Test Coverage Results" MODIFIED="1733935293549" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/OPALID_Cyl/Ellipsoid_lens" />
    <SUITE FILE_PATH="coverage/PycharmProject$waveshaper_wrapper.coverage" NAME="waveshaper_wrapper Coverage Results" MODIFIED="1750419966962" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/II-VI_waveshaper" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_mesh.coverage" NAME="export_mesh Coverage Results" MODIFIED="1750092795242" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D174_01" />
    <SUITE FILE_PATH="coverage/PycharmProject$Sonata_helper.coverage" NAME="Sonata_helper Coverage Results" MODIFIED="1755075065987" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/myutils" />
    <SUITE FILE_PATH="coverage/PycharmProject$straight_pump_to_EDWA.coverage" NAME="straight_pump_to_EDWA Coverage Results" MODIFIED="1732046053433" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/pump_to_EDWA/straight" />
    <SUITE FILE_PATH="coverage/PycharmProject$EPFL_D143_LGT_F2.coverage" NAME="EPFL_D143_LGT_F2 Coverage Results" MODIFIED="1750956478552" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$05_7deg_EDWA_to_fiber.coverage" NAME="05_7deg_EDWA_to_fiber Coverage Results" MODIFIED="1754426087447" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550_SQS" />
    <SUITE FILE_PATH="coverage/PycharmProject$coupling_optimizer.coverage" NAME="coupling_optimizer Coverage Results" MODIFIED="1747409483777" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple" />
    <SUITE FILE_PATH="coverage/PycharmProject$constellation_diagram.coverage" NAME="constellation_diagram Coverage Results" MODIFIED="1749732041345" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Optical_communication_101" />
    <SUITE FILE_PATH="coverage/PycharmProject$QD_MLLD_lens__1_.coverage" NAME="QD_MLLD_lens (1) Coverage Results" MODIFIED="1742459209271" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/../Temp/wpm_lens_design/designs/Hybrid_combs_packaging/low_backreflection/no_prism" />
    <SUITE FILE_PATH="coverage/PycharmProject$part_d.coverage" NAME="part_d Coverage Results" MODIFIED="1734426475313" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Teaching/ps04_student" />
    <SUITE FILE_PATH="coverage/PycharmProject$imported_structures.coverage" NAME="imported_structures Coverage Results" MODIFIED="1734425407659" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$plot_BER_over_channel.coverage" NAME="plot_BER_over_channel Coverage Results" MODIFIED="1753790802983" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/process_measurement/WDM" />
    <SUITE FILE_PATH="coverage/PycharmProject$build_lens_with_angled_gap.coverage" NAME="build_lens_with_angled_gap Coverage Results" MODIFIED="1739807292683" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/Inventor" />
    <SUITE FILE_PATH="coverage/PycharmProject$EPFL_500nm_test_wg.coverage" NAME="EPFL_500nm_test_wg Coverage Results" MODIFIED="1752847801749" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$Extract_facet_reflection__1_.coverage" NAME="Extract_facet_reflection (1) Coverage Results" MODIFIED="1753364880067" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/EDWA_characterization/Facet_reflection" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_fiber_lens.coverage" NAME="export_fiber_lens Coverage Results" MODIFIED="1754426564184" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1550_SQS" />
    <SUITE FILE_PATH="coverage/PycharmProject$8deg_pump_lens.coverage" NAME="8deg_pump_lens Coverage Results" MODIFIED="1730987458969" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/angled" />
    <SUITE FILE_PATH="coverage/PycharmProject$stl2depth.coverage" NAME="stl2depth Coverage Results" MODIFIED="1743603147551" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/nanoscribe" />
    <SUITE FILE_PATH="coverage/PycharmProject$Simple_lens.coverage" NAME="Simple_lens Coverage Results" MODIFIED="1729106532143" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wave_propagation_method/designs/SMF_to_AMF_EC_Alban" />
    <SUITE FILE_PATH="coverage/PycharmProject$ERC__1_.coverage" NAME="ERC (1) Coverage Results" MODIFIED="1725625503790" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ai_and_memory_wall" />
    <SUITE FILE_PATH="coverage/PycharmProject$EPFL_D211_doped_26cm.coverage" NAME="EPFL_D211_doped_26cm Coverage Results" MODIFIED="1753443968240" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$22_straight_fiber_lens_vlink_30MFD.coverage" NAME="22_straight_fiber_lens_vlink_30MFD Coverage Results" MODIFIED="1754910128303" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D143_LGT/C9_F6_opt_for_1550_gaussian" />
    <SUITE FILE_PATH="coverage/PycharmProject$start.coverage" NAME="start Coverage Results" MODIFIED="1725026514025" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/diffractio/playground" />
    <SUITE FILE_PATH="coverage/PycharmProject$y_straight_pump_lens__1_.coverage" NAME="y_straight_pump_lens (1) Coverage Results" MODIFIED="1730973331017" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/straight/intermediate_steps" />
    <SUITE FILE_PATH="coverage/PycharmProject$diss_figure.coverage" NAME="diss_figure Coverage Results" MODIFIED="1723551280785" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/yilin_wpm-main/designs/tilted_slab_test" />
    <SUITE FILE_PATH="coverage/PycharmProject$evenescent_coupling_blender.coverage" NAME="evenescent_coupling_blender Coverage Results" MODIFIED="1741704164061" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/PWB_NN/paper_plots" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_DFB_lens.coverage" NAME="export_DFB_lens Coverage Results" MODIFIED="1739209289323" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/DTU" />
    <SUITE FILE_PATH="coverage/PycharmProject$03_7deg_EDWA_lens_d4sigma_fit.coverage" NAME="03_7deg_EDWA_lens_d4sigma_fit Coverage Results" MODIFIED="1752858746754" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F1_doped_26cm_opt_for_1480" />
    <SUITE FILE_PATH="coverage/PycharmProject$GUI_2ch_plot.coverage" NAME="GUI_2ch_plot Coverage Results" MODIFIED="1739811507849" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Thorlab_PMxxx" />
    <SUITE FILE_PATH="coverage/PycharmProject$x_straight_pump_lens.coverage" NAME="x_straight_pump_lens Coverage Results" MODIFIED="1730976832899" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/MAGNIFY/straight/intermediate_steps" />
    <SUITE FILE_PATH="coverage/PycharmProject$01_straight_lens_d4sigma_fit.coverage" NAME="01_straight_lens_d4sigma_fit Coverage Results" MODIFIED="1751799208955" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D143_LGT/opt_for_1480" />
    <SUITE FILE_PATH="coverage/PycharmProject$front_end_ui.coverage" NAME="front_end_ui Coverage Results" MODIFIED="1724345145777" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/PyFocus/src/PyFocus/user_interface" />
    <SUITE FILE_PATH="coverage/PycharmProject$OrionEDFA_wrapper.coverage" NAME="OrionEDFA_wrapper Coverage Results" MODIFIED="1750422116664" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Orion_EDFA" />
    <SUITE FILE_PATH="coverage/PycharmProject$learn_signal.coverage" NAME="learn_signal Coverage Results" MODIFIED="1739969567088" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Playground" />
    <SUITE FILE_PATH="coverage/PycharmProject$QDMLLD_to_SiN.coverage" NAME="QDMLLD_to_SiN Coverage Results" MODIFIED="1729778772695" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/QD_MLLD_to_SiN/20deg" />
    <SUITE FILE_PATH="coverage/PycharmProject$obr_data_processing_CLEO_plot.coverage" NAME="obr_data_processing_CLEO_plot Coverage Results" MODIFIED="1744643403526" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/process_measurement/OBR_processing" />
    <SUITE FILE_PATH="coverage/PycharmProject$01_straight_EDWA_lens_single_fit.coverage" NAME="01_straight_EDWA_lens_single_fit Coverage Results" MODIFIED="1755091576223" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D211_large_mfd/F3_doped_26cm_opt_for_1550_SQS" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_SiC_lens.coverage" NAME="export_SiC_lens Coverage Results" MODIFIED="1739280627823" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/DTU" />
    <SUITE FILE_PATH="coverage/PycharmProject$mfd_meas.coverage" NAME="mfd_meas Coverage Results" MODIFIED="1726591936645" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/mfd_measurement/old" />
    <SUITE FILE_PATH="coverage/PycharmProject$mfd_fit.coverage" NAME="mfd_fit Coverage Results" MODIFIED="1727279061184" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement_processing/MFD_setup/mfd_fit" />
    <SUITE FILE_PATH="coverage/PycharmProject$utils.coverage" NAME="utils Coverage Results" MODIFIED="1753184729616" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/process_measurement/PNA" />
    <SUITE FILE_PATH="coverage/PycharmProject$wpms__1_.coverage" NAME="wpms (1) Coverage Results" MODIFIED="1725971439925" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/src/bvwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$Ligentec_SiN_Ring_angled_facet__1_.coverage" NAME="Ligentec_SiN_Ring_angled_facet (1) Coverage Results" MODIFIED="1728912453019" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$VLINK_16ch_pitch127um_lidless_fiberarray_with_lens.coverage" NAME="VLINK_16ch_pitch127um_lidless_fiberarray_with_lens Coverage Results" MODIFIED="1754409138087" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$SMF_lens.coverage" NAME="SMF_lens Coverage Results" MODIFIED="1729106007608" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wave_propagation_method/designs/SMF_to_AMF_EC_Alban" />
    <SUITE FILE_PATH="coverage/PycharmProject$OBR_rename.coverage" NAME="OBR_rename Coverage Results" MODIFIED="1721982032315" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/myutils" />
    <SUITE FILE_PATH="coverage/PycharmProject$31_straight_fiber_lens_vlink_30MFD.coverage" NAME="31_straight_fiber_lens_vlink_30MFD Coverage Results" MODIFIED="1754920414032" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D143_LGT/C9_F6_opt_for_1550_gaussian" />
    <SUITE FILE_PATH="coverage/PycharmProject$B2902A_ai.coverage" NAME="B2902A_ai Coverage Results" MODIFIED="1751396334412" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="C:\Users\<USER>\Documents\PycharmProject\device_wrappers" />
    <SUITE FILE_PATH="coverage/PycharmProject$images_to_video.coverage" NAME="images_to_video Coverage Results" MODIFIED="1752848174489" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/myutils" />
    <SUITE FILE_PATH="coverage/PycharmProject$OPALID_HHI_2021_updated.coverage" NAME="OPALID_HHI_2021_updated Coverage Results" MODIFIED="1721718784696" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/wave_propagation_method/designs/OPALID_cyl_Stefan" />
    <SUITE FILE_PATH="coverage/PycharmProject$polarization_measurement.coverage" NAME="polarization_measurement Coverage Results" MODIFIED="1722009144723" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/devices/MFD_setup" />
    <SUITE FILE_PATH="coverage/PycharmProject$EPFL_D211_doped_26cm_with_lens.coverage" NAME="EPFL_D211_doped_26cm_with_lens Coverage Results" MODIFIED="1754399352192" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/MFD_measurement/devices" />
    <SUITE FILE_PATH="coverage/PycharmProject$chip_rotation_along_y.coverage" NAME="chip_rotation_along_y Coverage Results" MODIFIED="1739276420048" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Tools" />
    <SUITE FILE_PATH="coverage/PycharmProject$export_model_EDWA.coverage" NAME="export_model_EDWA Coverage Results" MODIFIED="1754920543247" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/MAGNIFY/D143_LGT/C9_F6_opt_for_1550_gaussian" />
    <SUITE FILE_PATH="coverage/PycharmProject$wpms__2_.coverage" NAME="wpms (2) Coverage Results" MODIFIED="1725975046735" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/bvwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$FP_resonance.coverage" NAME="FP_resonance Coverage Results" MODIFIED="1753357032581" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/measurement/EDWA_characterization" />
    <SUITE FILE_PATH="coverage/PycharmProject$coupling_different_mfd.coverage" NAME="coupling_different_mfd Coverage Results" MODIFIED="1731860363231" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/analysis" />
    <SUITE FILE_PATH="coverage/PycharmProject$bwpms.coverage" NAME="bwpms Coverage Results" MODIFIED="1725880994790" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm" />
    <SUITE FILE_PATH="coverage/PycharmProject$repair_mesh.coverage" NAME="repair_mesh Coverage Results" MODIFIED="1738859941056" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/Inventor" />
    <SUITE FILE_PATH="coverage/PycharmProject$cylinder_lens_cut_left_ext.coverage" NAME="cylinder_lens_cut_left_ext Coverage Results" MODIFIED="1725540759502" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/scripts/OPALID_Cyl" />
    <SUITE FILE_PATH="coverage/PycharmProject$plot_lateral_offset_tolerance.coverage" NAME="plot_lateral_offset_tolerance Coverage Results" MODIFIED="1745157154499" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/QD_MLLD_to_SiN" />
    <SUITE FILE_PATH="coverage/PycharmProject$temp__1_.coverage" NAME="temp (1) Coverage Results" MODIFIED="1750254905660" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/II-VI_waveshaper" />
    <SUITE FILE_PATH="coverage/PycharmProject$DV1550AA_wrapper.coverage" NAME="DV1550AA_wrapper Coverage Results" MODIFIED="1750272385482" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/device_wrappers/Thorlabs_VOA_DV1550AA" />
    <SUITE FILE_PATH="coverage/PycharmProject$mfd40_single_surf_8deg.coverage" NAME="mfd40_single_surf_8deg Coverage Results" MODIFIED="1754032515812" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ripple/scripts/Rotating_lidar_David" />
    <SUITE FILE_PATH="coverage/PycharmProject$masks.coverage" NAME="masks Coverage Results" MODIFIED="1729266078692" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bvwpm/utils" />
    <SUITE FILE_PATH="coverage/PycharmProject$Part_d.coverage" NAME="Part d Coverage Results" MODIFIED="1734540398248" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Teaching/ps04_student" />
    <SUITE FILE_PATH="coverage/PycharmProject$plot_RF_spectrum.coverage" NAME="plot_RF_spectrum Coverage Results" MODIFIED="1733341813605" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/process_measurement/PNA" />
  </component>
</project>