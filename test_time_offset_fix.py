#!/usr/bin/env python3
"""
Test script to verify the time offset fix in IPQ-THSensors.py
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'myutils'))

from datetime import datetime, timezone, timedelta
import pandas as pd

# Import the modified function
from IPQ_THSensors import retrieve_sensor_data_for_measurement_type

def test_time_offset_compensation():
    """
    Test that the time offset compensation is working correctly.
    """
    print("Testing time offset compensation in retrieve_sensor_data_for_measurement_type...")
    
    # Test with a specific time range
    test_start = '2025-08-12T10:00:00Z'  # Local time 10:00
    test_stop = '2025-08-12T12:00:00Z'   # Local time 12:00
    
    print(f"Requesting data from {test_start} to {test_stop} (local time)")
    print("Expected behavior:")
    print("- Query should be sent to InfluxDB with times adjusted by -2 hours")
    print("- Retrieved timestamps should be converted back to local time (+2 hours)")
    
    try:
        # Test with a known sensor (this may fail if no data exists, but we can check the logic)
        data = retrieve_sensor_data_for_measurement_type(
            sensor_id='IPQ_TH14',
            measurement_type='temperature',
            start_datetime=test_start,
            stop_datetime=test_stop
        )
        
        if not data.empty:
            print(f"\nRetrieved {len(data)} data points")
            print("First few timestamps (should be in local time):")
            print(data.index[:5])
            print("\nLast few timestamps:")
            print(data.index[-5:])
        else:
            print("\nNo data retrieved (this is expected if no sensor data exists for the test period)")
            
    except Exception as e:
        print(f"\nError during test: {e}")
        print("This might be expected if the InfluxDB server is not accessible")
    
    print("\nTest completed. Check the console output above to verify the behavior.")

def test_default_parameters():
    """
    Test that the default parameter bug is fixed.
    """
    print("\n" + "="*60)
    print("Testing default parameter handling...")
    
    try:
        # This should not fail due to None parameters
        data = retrieve_sensor_data_for_measurement_type(
            sensor_id='IPQ_TH14',
            measurement_type='temperature'
            # start_datetime and stop_datetime are None (default)
        )
        
        print("✓ Default parameter handling works correctly")
        print("✓ No more 'NoneType' errors when start_datetime/stop_datetime are None")
        
    except Exception as e:
        print(f"✗ Error with default parameters: {e}")

if __name__ == "__main__":
    test_time_offset_compensation()
    test_default_parameters()
    
    print("\n" + "="*60)
    print("SUMMARY OF CHANGES MADE:")
    print("1. ✓ Fixed default parameter bug (lines 41-44)")
    print("2. ✓ Added 2-hour offset compensation for InfluxDB queries (lines 46-57)")
    print("3. ✓ Added timestamp conversion back to local time (lines 99-100)")
    print("4. ✓ Updated function documentation to explain the time offset handling")
    print("5. ✓ Added timedelta import for time calculations")
